# Shape Skew M 因子策略 - 严格按照原需求的简洁版本
from gm.api import *
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# ==================== 配置参数 ====================
STRATEGY_ID = '58d5321f-5d4b-11f0-b220-00e2699251ab'
TOKEN = '787cec949649eb140ea4ccccb14523af76110c8f'

# 策略参数 - 严格按照原需求
LOOKBACK_DAYS = 20        # 计算偏度的历史天数
TOP_N_STOCKS = 20         # 选择偏度最小的20只股票
REBALANCE_FREQUENCY = 7   # 调仓频率（天）- 周度调仓
INITIAL_CASH = 1000000    # 初始资金

# 回测时间
BACKTEST_START_DATE = '2024-07-01 09:30:00'
BACKTEST_END_DATE = '2024-12-31 15:00:00'

# ==================== 全局变量 ====================
selected_stocks = []      # 当前选中的股票
last_rebalance_date = None  # 上次调仓日期

# ==================== 核心因子计算函数 ====================
def calculate_shape_skew_m(symbol, end_date, lookback_days=20):
    """
    计算 shape_skew_m 因子 - 分钟收益率偏度的均值
    
    Args:
        symbol: 股票代码
        end_date: 结束日期
        lookback_days: 回看天数
    
    Returns:
        float: shape_skew_m 因子值（偏度均值）
    """
    try:
        # 获取过去20天的分钟K线数据
        minute_data = history_n(
            symbol=symbol,
            frequency='60s',  # 1分钟K线
            count=lookback_days * 240,  # 每天约240分钟
            fields='close,bob',
            end_time=end_date + ' 15:00:00',
            adjust=ADJUST_NONE,
            df=True
        )
        
        if minute_data is None or minute_data.empty:
            return None
            
        # 计算分钟收益率
        minute_data['returns'] = minute_data['close'].pct_change()
        minute_data = minute_data.dropna()
        
        if len(minute_data) < 100:  # 数据量太少
            return None
            
        # 按交易日分组
        minute_data['trade_date'] = pd.to_datetime(minute_data['bob']).dt.date
        daily_skewness = []
        
        # 计算每日的收益率偏度
        for date, group in minute_data.groupby('trade_date'):
            if len(group) >= 30:  # 每日至少30个数据点
                daily_returns = group['returns'].dropna()
                if len(daily_returns) >= 30:
                    # 计算偏度
                    mean_ret = np.mean(daily_returns)
                    std_ret = np.std(daily_returns)
                    if std_ret > 0:
                        skew_value = np.mean(((daily_returns - mean_ret) / std_ret) ** 3)
                        if not np.isnan(skew_value):
                            daily_skewness.append(skew_value)
        
        # 计算偏度均值 - shape_skew_m = 20个日偏度的平均值
        if len(daily_skewness) >= 10:  # 至少10个交易日的数据
            shape_skew_m = np.mean(daily_skewness)
            return shape_skew_m
        else:
            return None
            
    except Exception as e:
        print(f"计算 {symbol} 的 shape_skew_m 因子时出错: {e}")
        return None

def get_stock_universe():
    """获取全市场股票池"""
    try:
        # 获取沪市主板股票
        sh_stocks = get_instruments(exchanges='SHSE', sec_types=1, df=True)
        sh_symbols = []
        if sh_stocks is not None and not sh_stocks.empty:
            sh_symbols = [s for s in sh_stocks['symbol'].tolist() if s.startswith('SHSE.60')]
        
        # 获取深市股票
        sz_stocks = get_instruments(exchanges='SZSE', sec_types=1, df=True)
        sz_symbols = []
        if sz_stocks is not None and not sz_stocks.empty:
            sz_symbols = [s for s in sz_stocks['symbol'].tolist() 
                         if s.startswith('SZSE.000') or s.startswith('SZSE.002') or s.startswith('SZSE.300')]
        
        all_symbols = sh_symbols + sz_symbols
        print(f"获取全市场股票: {len(all_symbols)}只")
        return all_symbols
        
    except Exception as e:
        print(f"获取股票池失败: {e}")
        # 备用股票池
        backup_symbols = [
            'SHSE.600519', 'SHSE.600036', 'SHSE.600000', 'SHSE.600276', 'SHSE.600887',
            'SZSE.000001', 'SZSE.000002', 'SZSE.000858', 'SZSE.002415', 'SZSE.002594'
        ]
        print(f"使用备用股票池: {len(backup_symbols)}只")
        return backup_symbols

def calculate_factors_for_universe(trade_date):
    """为全市场股票计算shape_skew_m因子"""
    print(f"\n=== {trade_date} 开始计算因子 ===")
    
    # 获取股票池
    universe = get_stock_universe()
    if not universe:
        print("股票池为空，跳过因子计算")
        return {}
    
    factors = {}
    valid_count = 0
    
    # 计算因子
    for i, symbol in enumerate(universe):
        if i % 100 == 0:
            print(f"计算进度: {i+1}/{len(universe)} (有效: {valid_count})")
            
        factor_value = calculate_shape_skew_m(symbol, trade_date, LOOKBACK_DAYS)
        
        if factor_value is not None:
            factors[symbol] = factor_value
            valid_count += 1
            
        # 如果已经有足够的有效因子，可以提前结束
        if valid_count >= TOP_N_STOCKS * 5:  # 保证有足够的选择余地
            print(f"已获得足够有效因子({valid_count}个)，提前结束计算")
            break
            
    print(f"因子计算完成: {valid_count}/{len(universe)} 只股票有效")
    return factors

def select_stocks_by_factor(factors):
    """根据因子值选股 - 选择偏度最小的20只股票"""
    if not factors:
        return []
    
    # 按因子值排序（偏度从小到大）
    sorted_stocks = sorted(factors.items(), key=lambda x: x[1])
    
    # 选择偏度最小的20只股票
    selected = [stock[0] for stock in sorted_stocks[:TOP_N_STOCKS]]
    
    print(f"\n=== 选股结果 ===")
    print(f"候选股票总数: {len(factors)}")
    print(f"选中股票数量: {len(selected)}")
    
    # 显示选中的股票详情
    print(f"\n选中股票详情 (按偏度从小到大):")
    for i, (symbol, factor_value) in enumerate(sorted_stocks[:TOP_N_STOCKS]):
        print(f"{i+1:2d}. {symbol}: {factor_value:.4f}")
    
    return selected

# ==================== 策略核心函数 ====================
def init(context):
    """策略初始化"""
    global selected_stocks, last_rebalance_date
    
    print("=== Shape Skew M 因子策略初始化 ===")
    print(f"策略参数:")
    print(f"  回看天数: {LOOKBACK_DAYS}")
    print(f"  选股数量: {TOP_N_STOCKS}")
    print(f"  调仓频率: {REBALANCE_FREQUENCY}天")
    print(f"  初始资金: {INITIAL_CASH:,}")
    
    # 初始化变量
    selected_stocks = []
    last_rebalance_date = None
    
    # 设置定时任务 - 每天收盘后执行
    schedule(schedule_func=daily_check, date_rule='1d', time_rule='15:00:00')

def daily_check(context):
    """每日检查是否需要调仓"""
    global selected_stocks, last_rebalance_date
    
    current_date = context.now.strftime('%Y-%m-%d')
    
    # 检查是否需要调仓
    need_rebalance = False
    
    if last_rebalance_date is None:
        need_rebalance = True
        print(f"{current_date}: 首次调仓")
    else:
        days_since_rebalance = (context.now.date() - datetime.strptime(last_rebalance_date, '%Y-%m-%d').date()).days
        if days_since_rebalance >= REBALANCE_FREQUENCY:
            need_rebalance = True
            print(f"{current_date}: 距离上次调仓{days_since_rebalance}天，执行调仓")
    
    if need_rebalance:
        rebalance_portfolio(context, current_date)

def rebalance_portfolio(context, trade_date):
    """执行调仓"""
    global selected_stocks, last_rebalance_date
    
    print(f"\n{'='*50}")
    print(f"开始调仓: {trade_date}")
    print(f"{'='*50}")
    
    # 计算因子并选股
    factors = calculate_factors_for_universe(trade_date)
    if not factors:
        print("因子计算失败，跳过调仓")
        return
    
    new_selected_stocks = select_stocks_by_factor(factors)
    if not new_selected_stocks:
        print("选股失败，跳过调仓")
        return
    
    print(f"\n=== 执行交易 ===")
    
    # 清仓不在新选股列表中的股票
    current_positions = context.account().positions()
    for position in current_positions:
        if position['symbol'] not in new_selected_stocks:
            order_target_percent(
                symbol=position['symbol'], 
                percent=0,
                position_side=PositionSide_Long,
                order_type=OrderType_Market
            )
            print(f"清仓: {position['symbol']}")
    
    # 等权重买入新选中的股票
    target_weight = 0.95 / len(new_selected_stocks)  # 保留5%现金
    
    for symbol in new_selected_stocks:
        order_target_percent(
            symbol=symbol, 
            percent=target_weight,
            position_side=PositionSide_Long,
            order_type=OrderType_Market
        )
        print(f"买入: {symbol} (权重: {target_weight:.2%})")
    
    # 更新全局变量
    selected_stocks = new_selected_stocks
    last_rebalance_date = trade_date
    
    print(f"调仓完成: 持有{len(selected_stocks)}只股票，目标权重{target_weight:.2%}")

# ==================== 事件处理函数 ====================
def on_execution_report(context, execrpt):
    """委托执行回报"""
    try:
        symbol = getattr(execrpt, 'symbol', 'Unknown')
        side = getattr(execrpt, 'side', 'Unknown')
        volume = getattr(execrpt, 'volume', 0)
        price = getattr(execrpt, 'price', 0)
        print(f"委托执行: {symbol} {side} {volume}@{price:.2f}")
    except Exception as e:
        print(f"执行回报处理错误: {e}")

def on_account_status(context, account):
    """账户状态更新"""
    pass

def on_error(context, code, info):
    """错误处理"""
    print(f"策略错误 [{code}]: {info}")

# ==================== 策略启动 ====================
if __name__ == '__main__':
    run(strategy_id=STRATEGY_ID,
        filename='main.py',
        token=TOKEN,
        mode=MODE_BACKTEST,
        backtest_start_time=BACKTEST_START_DATE,
        backtest_end_time=BACKTEST_END_DATE,
        backtest_initial_cash=INITIAL_CASH)
