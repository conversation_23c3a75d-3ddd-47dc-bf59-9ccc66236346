# 常用模式和最佳实践

- 掘金SDK核心模式：init()初始化+subscribe()订阅数据+on_bar()处理K线+order_volume()下单+context.account()查询账户，推荐使用60s频率和row格式优化性能
- 掘金策略调试模式：添加详细的选股条件检查日志，包括涨幅、量比、换手率、市值、均线、RSI等每个条件的具体数值和通过状态
- 掘金策略数据获取最佳实践：使用get_symbols()获取完整的当日交易数据（包含昨收价），结合context.data()获取分钟线数据，避免使用current()在回测模式下的限制
- 掘金策略轮动模式：使用stk_get_index_constituents()获取指数成分股，get_symbols()过滤停牌ST股票，stk_get_daily_mktvalue_pt()获取市值数据，schedule()定时执行，order_target_percent()按比例下单
- 掘金策略动态股票池模式：在init()中计算指数收益率选择最佳指数，获取成分股并按市值排序，过滤停牌ST股票，建立动态更新的高质量股票池，提高策略适应性
- 掘金策略简化版模式：使用schedule()定时选股，context.data()获取分钟线数据，简单的涨幅条件选股，基本的止盈止损，避免复杂API调用，专注核心逻辑验证
- 掘金策略标准框架：配置层(参数化)、数据层(多频率订阅)、逻辑层(handle_daily_open/handle_minute_check)、交易层(buy_stock/sell_stock)、监控层(订单状态/统计报告)，90%代码复用率
- 日内选股策略实现：基于策略模板框架，实现涨幅2%-5%、量比>=2倍、换手率5%-10%、市值30-200亿、均线MA5>=MA10>MA20、RSI>50的选股条件，固定止盈7%、固定止损3%、浮动止损3%、破5日均线止损
- 日内选股策略多空力量对比实现：使用current()获取5档买卖盘口数据，计算总买量/总卖量比值，要求买盘力量>=1.2倍卖盘力量，判断资金流向和市场情绪，提高选股精确度
- 掘金策略选股条件展示优化：每个条件显示具体数值和要求范围，使用emoji图标区分不同指标，包含涨幅、量比、换手率、市值、均线、RSI、多空力量对比的详细数据展示
- 全市场选股策略实现：使用get_symbols()获取沪深两市所有A股，过滤停牌ST股票，排除科创板北交所，覆盖主板中小板创业板，实现真正的全市场选股覆盖
- 日内选股策略正确逻辑：记录当日开盘价，分钟线监控当前价相对开盘价的涨幅，当日内涨幅达到2%-5%时触发选股条件检查，而非使用昨收价对比
- 日内多时间点选股策略：在10:30、11:30、13:30、14:30、收盘等5个时间点检查日内涨幅，当任一时点涨幅达到2%-5%时触发选股，更好模拟真实日内交易
- 日内选股策略性能优化：改为每天只在14:20检查一次选股条件，避免多时间点检查增加计算负担，在股票数量较多时提高策略运行效率
- 掘金策略选股汇总功能：在回测完成时展示所有选中股票的详细信息，包括股票代码、选中日期、日内涨幅、量比、RSI、多空比等指标值，并计算平均值进行统计分析
- 股票池优化为沪深300：使用stk_get_index_constituents获取SHSE.000300成分股，简化过滤逻辑，直接使用300只成分股作为选股池，提供备用股票池确保策略稳定性
- 掘金回测真实盘口数据实现：订阅tick频率数据包含quotes字段，使用format='row'保持数据完整性，通过context.data()获取最新tick的盘口信息进行多空力量对比计算
- 多空力量对比模拟算法：基于量比、RSI、涨幅等技术指标模拟买卖力量比，综合计算公式为1.0+量比因子×0.3+RSI因子×0.4+涨幅因子×0.3，限制在0.5-3.0范围内
- 掘金Level-2真实盘口数据获取：使用get_history_l2ticks函数获取历史L2 Tick行情，提取quotes字段中的10档买卖盘口数据，计算真实的买卖盘力量对比
- 多空力量对比模块保留设计：暂时跳过检查但保留完整的实现框架和TODO注释，为将来获得真实盘口数据源时快速启用功能做准备
- 掘金策略订阅优化模式：借鉴雪球策略，采用统一订阅+最小化数据+动态扩展的模式，减少count从30到25，支持按需动态订阅新股票，提升性能
- 掘金策略漏斗式筛选重构：实现四层筛选(财务数据→价格条件→技术指标→实时数据)，批量预取财务数据，添加tick事件处理，模块化设计提升性能和可维护性
- 雪球策略学习成果：实现事件驱动模式(on_tick精确时间戳控制)，状态管理机制(PENDING/EXECUTED状态)，分阶段订阅策略(选股期25天数据→监控期1天数据)，信号生成机制(selection_signals类似trade_info_list)
- 选股逻辑重构：分离基础股票池构建(一次性财务筛选)和每日动态选股(涨幅+技术指标)，避免每天重复筛选市值换手率等相对稳定指标，提升效率和逻辑合理性
- 全市场股票池实现：使用get_instruments获取沪深两市所有A股，过滤ST股票，支持全市场选股，采用分批订阅策略避免系统过载，每日检查前500只活跃股票
- 掘金量化策略最佳实践：1.选股系统采用漏斗式多层筛选(财务→价格→技术→实时) 2.使用事件驱动交易信号(参考雪球策略trade_info_list模式) 3.动态订阅管理避免系统过载 4.双模式支持(实时11:00+14:00选股，回测15:15集中执行) 5.完整的监控体系(execution_log,account_status_log,error_log) 6.智能错误处理和资源清理机制
- 成功优化7个指标选股策略代码：从2296行优化到1587行（减少30.9%），通过合并重复筛选逻辑、删除未使用函数、简化日志输出等方式，保持100%功能完整性的同时大幅提升代码质量和执行效率
- 学习掘金量化示例代码，将选股策略从事件驱动改为定时任务驱动：使用schedule()函数设置11:00和14:00定时选股，解决了回测模式下依赖特定股票数据触发的问题，提高了策略执行的可靠性和精确性
- 掘金策略调试输出优化：为市值、换手率、均线、RSI、量比、涨幅等所有筛选条件添加详细的调试输出，包括具体数值、要求范围、通过/失败状态，并添加筛选通过率统计，实现漏斗式筛选效果的可视化展示
- 添加详细选股结果展示：在策略结束时显示当日选中股票的详细指标表格，包括市值、换手率、MA5/MA10/MA20、RSI、量比、涨幅等，并计算平均指标，方便分析选股效果和验证筛选条件
- 优化回测完成框架：在选中股票汇总中显示完整的选股指标表格，包括涨幅、市值、换手率、MA5/MA10/MA20、RSI、量比等详细信息，并计算平均值统计，让用户清楚看到每只选中股票通过的所有筛选条件数值
- NumPy优化策略实现：1.fast_skew函数替代scipy.stats.skew 2.batch_calculate_returns向量化收益率计算 3.multiprocessing.Pool并行计算不同股票批次 4.float32数据类型减少内存占用 5.智能回退机制确保稳定性
- 持久化缓存机制实现：1.使用pickle保存因子缓存到shape_skew_m_factor_cache.pkl文件 2.策略初始化时自动加载已有缓存 3.计算前检查缓存是否存在 4.计算完成后自动保存缓存 5.支持跨运行复用因子数据
