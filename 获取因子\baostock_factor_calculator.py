# BaoStock 因子计算脚本 - 预计算 shape_skew_m 因子
import baostock as bs
import pandas as pd
import numpy as np
import pickle
import os
from datetime import datetime, timedelta

# ==================== 配置参数 ====================
LOOKBACK_DAYS = 20        # 计算偏度的历史天数
CACHE_FILE = 'shape_skew_m_factor_cache.pkl'  # 缓存文件名

# 中证1000成分股代码（动态获取）
CSI1000_STOCKS = []

# ==================== 因子计算函数 ====================
def calculate_shape_skew_m_baostock(symbol, end_date, lookback_days=20):
    """
    使用BaoStock数据计算 shape_skew_m 因子
    
    Args:
        symbol: 股票代码 (如: sz.300887)
        end_date: 结束日期 (如: 2024-07-01)
        lookback_days: 回看天数
    
    Returns:
        float: shape_skew_m 因子值（偏度均值）
    """
    try:
        # 计算开始日期（多取一些天数以确保有足够数据）
        start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=lookback_days*2)).strftime('%Y-%m-%d')
        
        # 获取分钟K线数据 (1分钟)
        rs = bs.query_history_k_data_plus(
            symbol,
            "date,time,code,open,high,low,close,volume,amount,adjustflag",
            start_date=start_date,
            end_date=end_date,
            frequency="1",  # 1分钟K线
            adjustflag="3"  # 不复权
        )
        
        if rs.error_code != '0':
            print(f"获取 {symbol} 数据失败: {rs.error_msg}")
            return None
        
        # 转换为DataFrame
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        
        if not data_list:
            return None
            
        minute_data = pd.DataFrame(data_list, columns=rs.fields)
        
        # 数据预处理
        minute_data['close'] = pd.to_numeric(minute_data['close'], errors='coerce')
        minute_data['datetime'] = pd.to_datetime(minute_data['date'] + ' ' + minute_data['time'])
        minute_data = minute_data.dropna()
        
        if len(minute_data) < 100:
            return None
        
        # 计算分钟收益率
        minute_data['returns'] = minute_data['close'].pct_change()
        minute_data = minute_data.dropna()
        
        # 过滤异常收益率
        minute_data = minute_data[
            (minute_data['returns'] >= -0.2) & 
            (minute_data['returns'] <= 0.2)
        ]
        
        if len(minute_data) < 200:
            return None
        
        # 按交易日分组
        minute_data['trade_date'] = minute_data['datetime'].dt.date
        daily_skewness = []
        
        # 计算每日的收益率偏度
        for date, group in minute_data.groupby('trade_date'):
            if len(group) >= 50:  # 每日至少50个数据点
                daily_returns = group['returns'].values
                if len(daily_returns) >= 50:
                    # 计算偏度
                    mean_ret = np.mean(daily_returns)
                    std_ret = np.std(daily_returns)
                    if std_ret > 0:
                        skew_value = np.mean(((daily_returns - mean_ret) / std_ret) ** 3)
                        if not np.isnan(skew_value):
                            daily_skewness.append(skew_value)
        
        # 计算偏度均值
        if len(daily_skewness) >= 10:
            shape_skew_m = np.mean(daily_skewness)
            return shape_skew_m
        else:
            return None
            
    except Exception as e:
        print(f"计算 {symbol} 的 shape_skew_m 因子时出错: {e}")
        return None

def get_csi1000_stocks():
    """获取中证1000成分股列表"""
    try:
        # 尝试获取中证1000成分股
        rs = bs.query_zz1000_stocks()
        if rs.error_code != '0':
            print(f"获取中证1000失败: {rs.error_msg}")
            print("尝试使用中证500替代...")

            # 回退到中证500
            rs = bs.query_zz500_stocks()
            if rs.error_code != '0':
                print(f"获取中证500也失败: {rs.error_msg}")
                return []

        # 转换为DataFrame
        stock_list = []
        while (rs.error_code == '0') & rs.next():
            stock_list.append(rs.get_row_data())

        if not stock_list:
            print("成分股为空")
            return []

        stock_df = pd.DataFrame(stock_list, columns=rs.fields)
        stocks = stock_df['code'].tolist()

        print(f"获取成分股: {len(stocks)}只")
        return stocks

    except Exception as e:
        print(f"获取成分股失败: {e}")
        # 返回备用股票池
        backup_stocks = [
            'sz.300887', 'sh.688318', 'sz.002815', 'sh.603100', 'sz.002726',
            'sz.000001', 'sz.000002', 'sz.000858', 'sz.002415', 'sz.002594',
            'sh.600519', 'sh.600036', 'sh.600000', 'sh.600276', 'sh.600887'
        ]
        print(f"使用备用股票池: {len(backup_stocks)}只")
        return backup_stocks

def load_existing_cache():
    """加载已有的缓存"""
    if os.path.exists(CACHE_FILE):
        try:
            with open(CACHE_FILE, 'rb') as f:
                cache = pickle.load(f)
            print(f"加载已有缓存: {len(cache)}个交易日")
            return cache
        except Exception as e:
            print(f"加载缓存失败: {e}")
    return {}

def save_cache(cache):
    """保存缓存到文件"""
    try:
        with open(CACHE_FILE, 'wb') as f:
            pickle.dump(cache, f)
        print(f"缓存保存成功: {len(cache)}个交易日")
    except Exception as e:
        print(f"保存缓存失败: {e}")

def calculate_factors_for_date_range(start_date, end_date):
    """计算指定日期范围的所有因子"""
    print("=== BaoStock 因子批量计算 ===")
    
    # 登录BaoStock
    lg = bs.login()
    if lg.error_code != '0':
        print(f"BaoStock登录失败: {lg.error_msg}")
        return
    
    print("BaoStock登录成功")
    
    # 加载已有缓存
    factor_cache = load_existing_cache()
    
    # 获取股票列表
    stocks = get_csi1000_stocks()
    print(f"股票池: {len(stocks)}只")
    
    # 生成交易日列表
    trade_dates = pd.date_range(start=start_date, end=end_date, freq='D')
    trade_dates = [d.strftime('%Y-%m-%d') for d in trade_dates]
    
    total_calculations = 0
    successful_calculations = 0
    
    for trade_date in trade_dates:
        print(f"\n=== 计算 {trade_date} 的因子 ===")
        
        # 检查缓存
        if trade_date in factor_cache:
            print(f"{trade_date} 已在缓存中，跳过")
            continue
        
        factors = {}
        valid_count = 0
        
        for i, symbol in enumerate(stocks):
            if i % 50 == 0:
                print(f"进度: {i+1}/{len(stocks)} (有效: {valid_count})")
            
            factor_value = calculate_shape_skew_m_baostock(symbol, trade_date, LOOKBACK_DAYS)
            total_calculations += 1
            
            if factor_value is not None:
                factors[symbol] = factor_value
                valid_count += 1
                successful_calculations += 1
        
        if factors:
            factor_cache[trade_date] = factors
            print(f"{trade_date} 计算完成: {valid_count}/{len(stocks)} 只股票有效")
            
            # 定期保存缓存
            save_cache(factor_cache)
        else:
            print(f"{trade_date} 无有效因子数据")
    
    # 最终保存
    save_cache(factor_cache)
    
    # 登出BaoStock
    bs.logout()
    
    print(f"\n=== 批量计算完成 ===")
    print(f"总计算次数: {total_calculations}")
    print(f"成功计算: {successful_calculations}")
    print(f"成功率: {successful_calculations/total_calculations*100:.1f}%")
    print(f"缓存文件: {CACHE_FILE}")

# ==================== 主函数 ====================
if __name__ == '__main__':
    # 设置计算的日期范围
    START_DATE = '2024-06-01'
    END_DATE = '2024-12-31'
    
    print("=== BaoStock Shape Skew M 因子预计算 ===")
    print(f"计算日期范围: {START_DATE} 到 {END_DATE}")
    print(f"回看天数: {LOOKBACK_DAYS}")
    print(f"缓存文件: {CACHE_FILE}")
    
    # 开始批量计算
    calculate_factors_for_date_range(START_DATE, END_DATE)
