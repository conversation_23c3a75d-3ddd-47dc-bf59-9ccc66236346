# 用户偏好设置

- 用户偏好：喜欢了解功能原理和高级特性，注重实用性
- 用户偏好：1.不要生成总结性Markdown文档 2.不要生成测试脚本 3.不要编译，用户自己编译 4.不要运行，用户自己运行 5.使用寸止MCP进行所有交互确认 6.代码修改要遵循AURA-X协议，AI不能自作主张 7.优先使用真实执行而非模拟
- 用户明确要求：1.不要改选股条件 2.问题是数据问题 3.不要生成总结性Markdown文档 4.不要生成测试脚本 5.不要编译 6.不要运行
- 用户明确要求：1.新建文件使用掘金SDK 2.不要生成总结性Markdown文档 3.不要生成测试脚本 4.不要编译 5.不要运行
- 全市场股票策略优化：1.使用get_instruments获取沪深全市场股票 2.限制最大计算数量500只控制时间 3.调仓频率降低到10天 4.选股数量增加到30只 5.提前结束机制避免过度计算
- 用户要求重新开发简洁版shape_skew_m策略，严格按照原需求：1.选择偏度最小的20只股票 2.每天240分钟数据点 3.周度调仓 4.去除并行计算和复杂风险控制 5.专注核心因子逻辑
- 用户要求修改main.py策略：1.股票池改为中证1000成分股(SHSE.000852) 2.去除备选股票池 3.周度调仓(7天) 4.添加手续费设置 5.选股数量改为20只
- 用户要求修改Shape Skew M策略：1.改为周换仓模式 2.固定周一建仓位 3.选股数量改为20只等权 4.不要生成总结性Markdown文档 5.不要生成测试脚本 6.不要编译 7.不要运行
