# 策略拓展指南 - 基于现有框架快速开发新策略

## 📚 目录
1. [框架优势分析](#框架优势分析)
2. [可拓展策略类型](#可拓展策略类型)
3. [拓展步骤详解](#拓展步骤详解)
4. [具体策略示例](#具体策略示例)
5. [技术指标库](#技术指标库)
6. [快速开发模板](#快速开发模板)

---

## 框架优势分析

### 🏗️ 当前架构特点

您的代码具备优秀的**模块化设计**，各部分职责清晰：

```python
# 1. 配置层 - 参数化设计，易于修改
STOP_LOSS_RATIO = 0.01
TAKE_PROFIT_RATIO = 0.05
SYMBOLS = ['SHSE.600000', 'SZSE.000001']

# 2. 数据层 - 灵活的数据订阅
subscribe(frequency='1d')    # 日线数据
subscribe(frequency='60s')   # 分钟线数据

# 3. 逻辑层 - 模块化的策略处理
def handle_daily_open()      # 日线逻辑处理
def handle_minute_check()    # 分钟线逻辑处理

# 4. 交易层 - 标准化的交易接口
def buy_stock()              # 统一买入接口
def sell_stock()             # 统一卖出接口

# 5. 监控层 - 完善的监控和统计
def on_order_status()        # 订单状态监控
def on_backtest_finished()   # 统计报告生成
```

### ✅ 框架优势

1. **代码复用率高达90%** - 只需修改核心策略逻辑
2. **错误处理完善** - 资金不足、订单拒绝等异常处理
3. **日志系统专业** - 时间戳、交易记录、统计报告
4. **交易接口标准** - 支持各种品种和订单类型
5. **扩展性强** - 支持多频率数据、多种指标

---

## 可拓展策略类型

### 🟢 高度可行 (修改难度: ⭐⭐)

#### 1. 双均线策略
- **原理**: MA5上穿MA20买入，下穿卖出
- **修改点**: `handle_minute_check()` + 均线计算函数
- **适用场景**: 趋势跟踪，中长线交易

#### 2. 三均线策略
- **原理**: 短中长三条均线的排列关系
- **修改点**: 增加MA60，修改买卖条件
- **适用场景**: 更稳定的趋势确认

#### 3. 价格突破策略
- **原理**: 突破前期高点买入，跌破低点卖出
- **修改点**: 计算N日最高最低价
- **适用场景**: 强势股追涨，弱势股止损

### 🟡 中等可行 (修改难度: ⭐⭐⭐)

#### 4. RSI策略
- **原理**: RSI<30超卖买入，RSI>70超买卖出
- **修改点**: 添加RSI计算，修改买卖条件
- **适用场景**: 震荡市场，短线交易

#### 5. MACD策略
- **原理**: MACD金叉买入，死叉卖出
- **修改点**: 添加MACD计算，信号识别
- **适用场景**: 趋势确认，中线交易

#### 6. 布林带策略
- **原理**: 价格触及下轨买入，上轨卖出
- **修改点**: 布林带计算，边界判断
- **适用场景**: 区间震荡，均值回归

### 🟠 需要较多修改 (修改难度: ⭐⭐⭐⭐)

#### 7. 网格交易策略
- **原理**: 设置价格网格，网格间买卖
- **修改点**: 网格管理，仓位控制
- **适用场景**: 震荡市场，自动化交易

#### 8. 配对交易策略
- **原理**: 两只相关股票的价差交易
- **修改点**: 股票池配对，价差计算
- **适用场景**: 市场中性，套利交易

---

## 拓展步骤详解

### 第一步: 确定策略类型和参数

```python
# 示例：双均线策略参数
MA_SHORT = 5          # 短期均线
MA_LONG = 20          # 长期均线
MIN_VOLUME = 1000000  # 最小成交量过滤
```

### 第二步: 添加技术指标计算函数

```python
def calculate_ma(prices, period):
    """计算移动平均线"""
    if len(prices) < period:
        return None
    return sum(prices[-period:]) / period

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    # RSI计算逻辑
    pass
```

### 第三步: 修改核心策略逻辑

```python
def handle_minute_check(context, bar):
    """修改这个函数实现新策略"""
    symbol = bar.symbol
    current_price = bar.close
    
    # 获取历史数据
    hist_data = context.data(symbol=symbol, frequency='60s', count=25)
    prices = [d['close'] for d in hist_data]
    
    # 计算技术指标
    if len(prices) >= MA_LONG:
        ma_short = calculate_ma(prices, MA_SHORT)
        ma_long = calculate_ma(prices, MA_LONG)
        
        # 策略逻辑
        if should_buy(ma_short, ma_long, symbol, context):
            buy_stock(context, symbol, current_price)
        elif should_sell(ma_short, ma_long, symbol, context):
            sell_stock(context, symbol, current_price, context.positions[symbol], "均线信号")
```

### 第四步: 定义买卖条件

```python
def should_buy(ma_short, ma_long, symbol, context):
    """买入条件"""
    return (ma_short > ma_long and 
            symbol not in context.positions and 
            len(context.positions) < 3)

def should_sell(ma_short, ma_long, symbol, context):
    """卖出条件"""
    return (ma_short < ma_long and 
            symbol in context.positions)
```

### 第五步: 调整策略参数

```python
# 根据策略特点调整
POSITION_SIZE_RATIO = 0.2  # 每次投入20%资金
MAX_POSITIONS = 3          # 最多持有3只股票
```

---

## 具体策略示例

### 示例1: 双均线策略完整代码

```python
# 在原代码基础上的修改部分

# 1. 添加策略参数
MA_SHORT = 5
MA_LONG = 20

# 2. 添加技术指标函数
def calculate_ma(prices, period):
    """计算移动平均线"""
    if len(prices) < period:
        return None
    return sum(prices[-period:]) / period

# 3. 修改核心逻辑
def handle_minute_check(context, bar):
    """双均线策略逻辑"""
    symbol = bar.symbol
    current_price = bar.close
    
    # 获取历史数据
    hist_data = context.data(symbol=symbol, frequency='60s', count=25)
    if not hist_data or len(hist_data) < MA_LONG:
        return
    
    prices = [d['close'] for d in hist_data]
    
    # 计算双均线
    ma5 = calculate_ma(prices, MA_SHORT)
    ma20 = calculate_ma(prices, MA_LONG)
    
    if ma5 is None or ma20 is None:
        return
    
    # 金叉买入
    if (ma5 > ma20 and 
        symbol not in context.positions and 
        len(context.positions) < 2):
        buy_stock(context, symbol, current_price)
    
    # 死叉卖出
    elif (ma5 < ma20 and 
          symbol in context.positions):
        position = context.positions[symbol]
        sell_stock(context, symbol, current_price, position, "均线死叉")

# 4. 可选：修改日线逻辑为空（不在开盘买入）
def handle_daily_open(context, bar):
    """双均线策略不需要开盘买入"""
    # 每天重置买入记录
    if not hasattr(context, 'last_date') or context.last_date != bar.eob.date():
        context.bought_today = set()
        context.last_date = bar.eob.date()
        print(f"\n[{context.now}] 新交易日")
    # 不执行开盘买入逻辑
```

### 示例2: RSI策略完整代码

```python
# RSI策略修改部分

# 1. 添加RSI参数
RSI_PERIOD = 14
RSI_OVERSOLD = 30
RSI_OVERBOUGHT = 70

# 2. 添加RSI计算函数
def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    if len(prices) < period + 1:
        return None
    
    gains = []
    losses = []
    
    for i in range(1, len(prices)):
        change = prices[i] - prices[i-1]
        if change > 0:
            gains.append(change)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(-change)
    
    if len(gains) < period:
        return None
    
    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period
    
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

# 3. 修改核心逻辑
def handle_minute_check(context, bar):
    """RSI策略逻辑"""
    symbol = bar.symbol
    current_price = bar.close
    
    # 获取历史数据
    hist_data = context.data(symbol=symbol, frequency='60s', count=30)
    if not hist_data or len(hist_data) < RSI_PERIOD + 1:
        return
    
    prices = [d['close'] for d in hist_data]
    
    # 计算RSI
    rsi = calculate_rsi(prices, RSI_PERIOD)
    if rsi is None:
        return
    
    # RSI超卖买入
    if (rsi < RSI_OVERSOLD and 
        symbol not in context.positions and 
        len(context.positions) < 2):
        buy_stock(context, symbol, current_price)
        print(f"[{context.now}] RSI超卖买入: {symbol} RSI={rsi:.2f}")
    
    # RSI超买卖出
    elif (rsi > RSI_OVERBOUGHT and 
          symbol in context.positions):
        position = context.positions[symbol]
        sell_stock(context, symbol, current_price, position, "RSI超买")
        print(f"[{context.now}] RSI超买卖出: {symbol} RSI={rsi:.2f}")
```

---

## 技术指标库

### 趋势指标

```python
def calculate_ma(prices, period):
    """简单移动平均线"""
    if len(prices) < period:
        return None
    return sum(prices[-period:]) / period

def calculate_ema(prices, period):
    """指数移动平均线"""
    if len(prices) < period:
        return None
    
    multiplier = 2 / (period + 1)
    ema = prices[0]
    
    for price in prices[1:]:
        ema = (price * multiplier) + (ema * (1 - multiplier))
    
    return ema

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """MACD指标"""
    if len(prices) < slow:
        return None, None, None
    
    ema_fast = calculate_ema(prices, fast)
    ema_slow = calculate_ema(prices, slow)
    
    if ema_fast is None or ema_slow is None:
        return None, None, None
    
    macd_line = ema_fast - ema_slow
    # 简化版本，实际需要计算signal线的EMA
    signal_line = macd_line  # 这里需要完整实现
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram
```

### 震荡指标

```python
def calculate_rsi(prices, period=14):
    """RSI相对强弱指标"""
    # 前面已提供完整代码
    pass

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """布林带指标"""
    if len(prices) < period:
        return None, None, None
    
    # 计算中轨（移动平均线）
    middle = calculate_ma(prices, period)
    if middle is None:
        return None, None, None
    
    # 计算标准差
    recent_prices = prices[-period:]
    variance = sum([(price - middle) ** 2 for price in recent_prices]) / period
    std = variance ** 0.5
    
    # 计算上下轨
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    
    return upper, middle, lower

def calculate_stoch(highs, lows, closes, k_period=14, d_period=3):
    """随机指标KDJ"""
    if len(closes) < k_period:
        return None, None
    
    # 计算%K
    recent_highs = highs[-k_period:]
    recent_lows = lows[-k_period:]
    current_close = closes[-1]
    
    highest_high = max(recent_highs)
    lowest_low = min(recent_lows)
    
    if highest_high == lowest_low:
        k_percent = 50
    else:
        k_percent = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100
    
    # 计算%D（%K的移动平均）
    # 简化版本，实际需要维护%K的历史值
    d_percent = k_percent
    
    return k_percent, d_percent
```

### 成交量指标

```python
def calculate_volume_ma(volumes, period):
    """成交量移动平均"""
    if len(volumes) < period:
        return None
    return sum(volumes[-period:]) / period

def calculate_obv(prices, volumes):
    """能量潮指标"""
    if len(prices) < 2 or len(volumes) < 2:
        return None
    
    obv = 0
    for i in range(1, len(prices)):
        if prices[i] > prices[i-1]:
            obv += volumes[i]
        elif prices[i] < prices[i-1]:
            obv -= volumes[i]
        # 价格相等时OBV不变
    
    return obv
```

---

## 快速开发模板

### 策略开发检查清单

#### ✅ 开发前准备
- [ ] 确定策略类型和交易逻辑
- [ ] 选择合适的数据频率
- [ ] 设计技术指标和参数
- [ ] 规划风险控制措施

#### ✅ 代码修改步骤
- [ ] 添加策略参数配置
- [ ] 实现技术指标计算函数
- [ ] 修改 `handle_minute_check()` 核心逻辑
- [ ] 调整 `handle_daily_open()` 逻辑（如需要）
- [ ] 测试买卖条件逻辑
- [ ] 验证风险控制机制

#### ✅ 测试验证
- [ ] 单元测试技术指标计算
- [ ] 回测验证策略表现
- [ ] 检查日志输出正确性
- [ ] 验证统计报告准确性

### 通用策略模板

```python
# ==================== 策略参数配置 ====================
# 在原有参数基础上添加策略特定参数
STRATEGY_TYPE = "双均线策略"  # 策略名称
MA_SHORT = 5                 # 短期均线
MA_LONG = 20                 # 长期均线
MIN_VOLUME = 1000000         # 最小成交量过滤

# ==================== 技术指标函数 ====================
def calculate_indicator(data, params):
    """
    通用技术指标计算模板

    Args:
        data: 历史数据
        params: 指标参数

    Returns:
        指标值或None
    """
    # 数据验证
    if not data or len(data) < params['min_period']:
        return None

    # 指标计算逻辑
    result = None  # 实现具体计算

    return result

# ==================== 策略逻辑函数 ====================
def should_buy(context, symbol, current_price, indicators):
    """
    买入条件判断

    Args:
        context: 策略上下文
        symbol: 股票代码
        current_price: 当前价格
        indicators: 技术指标字典

    Returns:
        bool: 是否应该买入
    """
    # 基本条件检查
    if (symbol in context.positions or
        len(context.positions) >= 2):
        return False

    # 策略特定条件
    # 例如：双均线金叉
    if indicators.get('ma_short', 0) > indicators.get('ma_long', 0):
        return True

    return False

def should_sell(context, symbol, current_price, indicators):
    """
    卖出条件判断

    Args:
        context: 策略上下文
        symbol: 股票代码
        current_price: 当前价格
        indicators: 技术指标字典

    Returns:
        tuple: (是否卖出, 卖出原因)
    """
    if symbol not in context.positions:
        return False, ""

    # 策略特定条件
    # 例如：双均线死叉
    if indicators.get('ma_short', 0) < indicators.get('ma_long', 0):
        return True, "均线死叉"

    return False, ""

# ==================== 修改核心逻辑 ====================
def handle_minute_check(context, bar):
    """
    分钟线处理 - 策略核心逻辑
    """
    symbol = bar.symbol
    current_price = bar.close

    # 获取历史数据
    hist_data = context.data(symbol=symbol, frequency='60s', count=30)
    if not hist_data or len(hist_data) < MA_LONG:
        return

    # 提取价格数据
    prices = [d['close'] for d in hist_data]
    volumes = [d['volume'] for d in hist_data]

    # 计算技术指标
    indicators = {
        'ma_short': calculate_ma(prices, MA_SHORT),
        'ma_long': calculate_ma(prices, MA_LONG),
        'volume_ma': calculate_volume_ma(volumes, 10)
    }

    # 数据验证
    if any(v is None for v in indicators.values()):
        return

    # 成交量过滤
    if bar.volume < MIN_VOLUME:
        return

    # 买入逻辑
    if should_buy(context, symbol, current_price, indicators):
        buy_stock(context, symbol, current_price)
        print(f"[{context.now}] 策略买入: {symbol} MA5={indicators['ma_short']:.2f} MA20={indicators['ma_long']:.2f}")

    # 卖出逻辑
    should_sell_flag, sell_reason = should_sell(context, symbol, current_price, indicators)
    if should_sell_flag:
        position = context.positions[symbol]
        sell_stock(context, symbol, current_price, position, sell_reason)
        print(f"[{context.now}] 策略卖出: {symbol} 原因:{sell_reason}")
```

---

## 实用开发技巧

### 1. 调试技巧

```python
# 添加调试信息
def debug_print(context, symbol, indicators):
    """打印调试信息"""
    if hasattr(context, 'debug') and context.debug:
        print(f"[DEBUG] {symbol}: MA5={indicators.get('ma_short', 0):.2f}, "
              f"MA20={indicators.get('ma_long', 0):.2f}, "
              f"持仓:{symbol in context.positions}")

# 在init函数中启用调试
def init(context):
    # 原有初始化代码...
    context.debug = True  # 启用调试模式
```

### 2. 参数优化

```python
# 参数配置字典，便于批量测试
STRATEGY_PARAMS = {
    'ma_short': [3, 5, 10],
    'ma_long': [15, 20, 30],
    'volume_threshold': [500000, 1000000, 2000000]
}

# 参数验证函数
def validate_params():
    """验证参数合理性"""
    assert MA_SHORT < MA_LONG, "短期均线周期必须小于长期均线"
    assert POSITION_SIZE_RATIO <= 1.0, "持仓比例不能超过100%"
    assert len(SYMBOLS) > 0, "股票池不能为空"
```

### 3. 性能优化

```python
# 缓存技术指标计算结果
def get_cached_indicators(context, symbol, hist_data):
    """缓存指标计算结果"""
    cache_key = f"{symbol}_{len(hist_data)}"

    if not hasattr(context, 'indicator_cache'):
        context.indicator_cache = {}

    if cache_key not in context.indicator_cache:
        prices = [d['close'] for d in hist_data]
        context.indicator_cache[cache_key] = {
            'ma_short': calculate_ma(prices, MA_SHORT),
            'ma_long': calculate_ma(prices, MA_LONG)
        }

    return context.indicator_cache[cache_key]
```

### 4. 风险控制增强

```python
def enhanced_risk_control(context, symbol, current_price):
    """增强风险控制"""
    # 单日最大交易次数限制
    if not hasattr(context, 'daily_trade_count'):
        context.daily_trade_count = {}

    today = context.now.date()
    if today not in context.daily_trade_count:
        context.daily_trade_count[today] = 0

    if context.daily_trade_count[today] >= 10:  # 单日最多10次交易
        return False

    # 单只股票持仓时间限制
    if symbol in context.positions:
        position = context.positions[symbol]
        hold_time = context.now - position.get('buy_time', context.now)
        if hold_time.days > 30:  # 持仓超过30天强制卖出
            return True

    return False
```

---

## 常见问题解决

### Q1: 技术指标计算不准确
```python
# 解决方案：增加数据验证
def safe_calculate_ma(prices, period):
    """安全的均线计算"""
    if not prices or len(prices) < period:
        return None

    # 过滤异常数据
    valid_prices = [p for p in prices if p > 0]
    if len(valid_prices) < period:
        return None

    return sum(valid_prices[-period:]) / period
```

### Q2: 策略信号过于频繁
```python
# 解决方案：添加信号过滤
def signal_filter(context, symbol, signal_type):
    """信号过滤器"""
    if not hasattr(context, 'last_signal'):
        context.last_signal = {}

    last_signal_time = context.last_signal.get(symbol, None)
    if last_signal_time:
        time_diff = context.now - last_signal_time
        if time_diff.seconds < 300:  # 5分钟内不重复信号
            return False

    context.last_signal[symbol] = context.now
    return True
```

### Q3: 回测结果不理想
```python
# 解决方案：增加策略评估
def strategy_evaluation(context, indicator):
    """策略评估"""
    # 计算更多指标
    max_consecutive_losses = 0  # 最大连续亏损次数
    profit_factor = 0           # 盈亏比

    # 分析交易记录
    for trade in context.trade_log:
        # 实现详细分析逻辑
        pass

    print(f"策略评估:")
    print(f"最大连续亏损: {max_consecutive_losses}次")
    print(f"盈亏比: {profit_factor:.2f}")
```

---

## 总结

通过这个拓展指南，您可以：

1. **快速开发新策略** - 90%代码复用，只需修改核心逻辑
2. **使用丰富的技术指标** - 提供常用指标计算函数
3. **遵循最佳实践** - 包含调试、优化、风险控制技巧
4. **避免常见问题** - 提供问题解决方案

您的策略框架是一个**优秀的量化交易开发平台**，具备了专业级的功能和扩展性！🚀
