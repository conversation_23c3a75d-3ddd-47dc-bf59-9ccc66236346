# 掘金SDK完整学习指南

## 📚 目录
1. [基本函数](#基本函数)
2. [数据订阅](#数据订阅)
3. [数据事件](#数据事件)
4. [行情数据查询](#行情数据查询)
5. [交易函数](#交易函数)
6. [数据结构](#数据结构)
7. [策略模板](#策略模板)
8. [最佳实践](#最佳实践)

---

## 基本函数

### 策略运行函数
```python
# 运行策略
run(strategy_id='your_strategy_id',
    filename='main.py',
    mode=MODE_BACKTEST,  # MODE_BACKTEST回测 / MODE_LIVE实盘
    token='your_token',
    backtest_start_time='2025-01-01 09:30:00',
    backtest_end_time='2025-01-31 15:00:00',
    backtest_adjust=ADJUST_PREV,  # 复权方式
    backtest_initial_cash=1000000,
    backtest_commission_ratio=0.0003,
    backtest_slippage_ratio=0.0001)

# 设置token
set_token('your_token')
```

### 核心策略函数
```python
def init(context):
    """策略初始化 - 必须实现"""
    pass

def on_bar(context, bars):
    """K线数据事件 - 推荐使用"""
    pass

def on_tick(context, tick):
    """tick数据事件"""
    pass

def on_order_status(context, order):
    """订单状态变化事件"""
    pass

def on_backtest_finished(context, indicator):
    """回测结束事件"""
    pass
```

---

## 数据订阅

### subscribe函数详解
```python
# 基本订阅
subscribe(symbols=['SHSE.600000', 'SZSE.000001'], 
         frequency='60s',  # tick, 60s, 300s, 1d等
         count=20,         # 数据滑窗大小
         fields='symbol,open,high,low,close,volume,eob',  # 指定字段提高性能
         format='row',     # row > col > df (性能排序)
         unsubscribe_previous=False)

# 多频率订阅
subscribe(symbols=symbols, frequency='1d', count=1)    # 日线
subscribe(symbols=symbols, frequency='60s', count=5)   # 分钟线
```

### 频率选择指南
- **tick**: 实时分笔数据，高频交易
- **60s**: 1分钟K线，适合短线策略
- **300s**: 5分钟K线，适合中短线策略
- **1d**: 日线，适合中长线策略

### 性能优化
```python
# 最佳实践
subscribe(symbols=symbols,
         frequency='60s',
         count=10,
         fields='symbol,close,eob',  # 只订阅需要的字段
         format='row')               # 使用最高效格式
```

---

## 数据事件

### on_bar vs on_tick
```python
def on_bar(context, bars):
    """处理K线数据 - 推荐用于止盈止损策略"""
    for bar in bars:
        symbol = bar.symbol
        frequency = bar.frequency
        current_price = bar.close
        
        if frequency == '1d':
            # 处理日线数据
            handle_daily_logic(context, bar)
        elif frequency == '60s':
            # 处理分钟线数据
            handle_minute_logic(context, bar)

def on_tick(context, tick):
    """处理tick数据 - 用于高频交易"""
    symbol = tick.symbol
    price = tick.price
    volume = tick.volume
```

### 数据结构访问
```python
# Bar对象属性
bar.symbol      # 股票代码
bar.frequency   # 数据频率
bar.open        # 开盘价
bar.high        # 最高价
bar.low         # 最低价
bar.close       # 收盘价
bar.volume      # 成交量
bar.amount      # 成交额
bar.eob         # 结束时间
bar.bob         # 开始时间

# Tick对象属性
tick.symbol     # 股票代码
tick.price      # 最新价
tick.volume     # 成交量
tick.created_at # 时间戳
```

---

## 行情数据查询

### 实时数据查询
```python
# 查询当前行情快照
current_data = current(symbols=['SHSE.600000'])
price = current_data[0]['price']

# 查询当前最新价
price_data = current_price('SHSE.600000')
latest_price = price_data[0]['price']
```

### 历史数据查询
```python
# 查询历史行情
hist_data = history(symbol='SHSE.600000',
                   frequency='1d',
                   start_time='2025-01-01',
                   end_time='2025-01-31',
                   fields='open,high,low,close,volume',
                   df=False)

# 查询最近N条数据
recent_data = history_n(symbol='SHSE.600000',
                       frequency='1d',
                       count=20,
                       fields='close',
                       df=False)
```

### 订阅数据查询
```python
# 查询订阅的数据滑窗
data = context.data(symbol='SHSE.600000',
                   frequency='60s',
                   count=10,
                   fields='close',
                   format='row')
```

---

## 交易函数

### 基本下单函数
```python
# 按股数下单 - 最常用
order = order_volume(symbol='SHSE.600000',
                    volume=1000,
                    side=OrderSide_Buy,          # Buy/Sell
                    order_type=OrderType_Market, # Market/Limit
                    position_effect=PositionEffect_Open,  # Open/Close
                    price=0)  # 市价单设为0

# 按金额下单
orders = order_value(symbol='SHSE.600000',
                    value=100000,
                    side=OrderSide_Buy,
                    order_type=OrderType_Market,
                    position_effect=PositionEffect_Open,
                    price=current_price)  # 需要指定价格计算股数

# 按比例下单
orders = order_percent(symbol='SHSE.600000',
                      percent=0.1,  # 10%资金
                      side=OrderSide_Buy,
                      order_type=OrderType_Market,
                      position_effect=PositionEffect_Open)
```

### 调仓函数
```python
# 调仓到目标股数
order_target_volume(symbol='SHSE.600000', volume=2000)

# 调仓到目标金额
order_target_value(symbol='SHSE.600000', value=200000)

# 调仓到目标比例
order_target_percent(symbol='SHSE.600000', percent=0.15)
```

### 订单管理
```python
# 查询所有委托
all_orders = get_orders()

# 查询未完成委托
unfinished_orders = get_unfinished_orders()

# 查询执行回报
exec_reports = get_execution_reports()

# 撤单
order_cancel(order_id)
order_cancel_all()  # 撤销所有未完成订单
```

### 账户查询
```python
# 查询账户资金
account_cash = context.account().cash
total_value = account_cash['nav']        # 总资产
available_cash = account_cash['available'] # 可用资金

# 查询持仓
position = context.account().position(symbol='SHSE.600000', 
                                     side=PositionSide_Long)
if position:
    volume = position['volume']          # 持仓数量
    available = position['available']    # 可用数量
    volume_today = position['volume_today']  # 今日买入数量
    available_volume = available - volume_today  # 可卖数量
```

---

## 数据结构

### 订单状态
```python
# 订单状态码
order.status == 1   # 未报
order.status == 2   # 待报
order.status == 3   # 已报(完全成交)
order.status == 5   # 部分成交
order.status == 8   # 已拒绝

# 订单信息
order.symbol        # 股票代码
order.side          # 买卖方向
order.volume        # 委托数量
order.price         # 委托价格
order.filled_volume # 成交数量
order.filled_vwap   # 成交均价
order.filled_amount # 成交金额
order.ord_rej_reason_detail  # 拒绝原因
```

### 品种特殊处理
```python
# 科创板
if symbol[:7] == 'SHSE.68':
    min_volume = 200  # 最小200股

# 可转债
if symbol[:7] in ['SHSE.11', 'SZSE.12']:
    order_type = OrderType_Limit  # 限价单

# 普通股票
else:
    order_type = OrderType_Market  # 市价单
```

---

## 策略模板

### 基础策略框架
```python
from gm.api import *
from datetime import datetime

# 配置参数
STRATEGY_ID = 'your_strategy_id'
TOKEN = 'your_token'
SYMBOLS = ['SHSE.600000', 'SZSE.000001']

def init(context):
    """初始化"""
    # 订阅数据
    subscribe(symbols=SYMBOLS, frequency='60s', count=10)
    
    # 初始化变量
    context.positions = {}

def on_bar(context, bars):
    """K线处理"""
    for bar in bars:
        symbol = bar.symbol
        current_price = bar.close
        
        # 策略逻辑
        if should_buy(context, symbol, current_price):
            buy_stock(context, symbol, current_price)
        elif should_sell(context, symbol, current_price):
            sell_stock(context, symbol, current_price)

def should_buy(context, symbol, price):
    """买入条件"""
    return True  # 实现具体逻辑

def should_sell(context, symbol, price):
    """卖出条件"""
    return False  # 实现具体逻辑

def buy_stock(context, symbol, price):
    """买入股票"""
    try:
        volume = 1000  # 计算买入数量
        order = order_volume(symbol=symbol, volume=volume,
                           side=OrderSide_Buy, order_type=OrderType_Market,
                           position_effect=PositionEffect_Open, price=0)
        if order:
            print(f"[{context.now}] 买入: {symbol} {volume}股")
    except Exception as e:
        print(f"买入失败: {e}")

def sell_stock(context, symbol, price):
    """卖出股票"""
    try:
        position = context.account().position(symbol=symbol, side=PositionSide_Long)
        if position:
            volume = position['available'] - position['volume_today']
            if volume > 0:
                order = order_volume(symbol=symbol, volume=volume,
                                   side=OrderSide_Sell, order_type=OrderType_Market,
                                   position_effect=PositionEffect_Close, price=0)
                if order:
                    print(f"[{context.now}] 卖出: {symbol} {volume}股")
    except Exception as e:
        print(f"卖出失败: {e}")

def on_order_status(context, order):
    """订单状态处理"""
    if order.status == 8:  # 拒绝
        print(f"订单拒绝: {order.symbol} {order.ord_rej_reason_detail}")
    elif order.status == 3:  # 成交
        side = "买入" if order.position_effect == PositionEffect_Open else "卖出"
        print(f"成交: {side}{order.symbol} {order.filled_volume}股")

if __name__ == '__main__':
    run(strategy_id=STRATEGY_ID, filename='main.py', mode=MODE_BACKTEST,
        token=TOKEN, backtest_start_time='2025-01-01 09:30:00',
        backtest_end_time='2025-01-31 15:00:00',
        backtest_initial_cash=1000000)
```

---

## 最佳实践

### 1. 错误处理
```python
def on_order_status(context, order):
    """智能订单处理"""
    try:
        if order.status == 8:  # 拒绝
            if '资金不足' in order.ord_rej_reason_detail:
                # 降低买入量重试
                account_cash = context.account().cash
                available = account_cash['available']
                current_data = current(symbols=order.symbol)[0]
                new_volume = int(available / current_data['price'] / 100) * 100
                if new_volume >= 100:
                    order_volume(symbol=order.symbol, volume=new_volume,
                               side=OrderSide_Buy, order_type=OrderType_Market,
                               position_effect=PositionEffect_Open, price=0)
    except Exception as e:
        print(f"订单处理异常: {e}")
```

### 2. 资金管理
```python
def calculate_position_size(context, symbol, price, risk_ratio=0.1):
    """计算合理的持仓大小"""
    account_cash = context.account().cash
    available = account_cash['available']

    # 按风险比例计算
    target_amount = available * risk_ratio
    volume = int(target_amount / price / 100) * 100

    # 科创板特殊处理
    if symbol[:7] == 'SHSE.68':
        volume = min(200, volume) if volume > 0 else 200
    elif volume < 100:
        volume = 100

    return volume
```

### 3. 时间管理
```python
def is_trading_time(context):
    """判断是否在交易时间"""
    now = context.now
    time_str = now.strftime('%H:%M')

    # 上午: 09:30-11:30, 下午: 13:00-15:00
    morning = '09:30' <= time_str <= '11:30'
    afternoon = '13:00' <= time_str <= '15:00'

    return morning or afternoon

def is_market_open(context):
    """判断是否开盘时间"""
    time_str = context.now.strftime('%H:%M')
    return time_str == '09:30'
```

### 4. 技术指标计算
```python
def calculate_ma(data, period):
    """计算移动平均线"""
    if len(data) < period:
        return None
    return sum(data[-period:]) / period

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    if len(prices) < period + 1:
        return None

    gains = []
    losses = []

    for i in range(1, len(prices)):
        change = prices[i] - prices[i-1]
        if change > 0:
            gains.append(change)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(-change)

    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period

    if avg_loss == 0:
        return 100

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi
```

---

## 常见策略类型

### 1. 开盘价买入止盈止损策略
```python
def init(context):
    subscribe(symbols=SYMBOLS, frequency='1d', count=1)    # 日线
    subscribe(symbols=SYMBOLS, frequency='60s', count=1)   # 分钟线
    context.positions = {}

def on_bar(context, bars):
    for bar in bars:
        if bar.frequency == '1d':
            # 开盘买入
            if should_buy_today(context, bar.symbol):
                buy_at_open(context, bar.symbol, bar.open)
        elif bar.frequency == '60s':
            # 止盈止损检查
            check_stop_profit_loss(context, bar)
```

### 2. 均线策略
```python
def on_bar(context, bars):
    for bar in bars:
        symbol = bar.symbol

        # 获取历史数据计算均线
        hist_data = context.data(symbol=symbol, frequency='60s', count=20)
        if len(hist_data) >= 20:
            prices = [d['close'] for d in hist_data]
            ma5 = calculate_ma(prices, 5)
            ma20 = calculate_ma(prices, 20)

            # 金叉买入，死叉卖出
            if ma5 > ma20 and not has_position(context, symbol):
                buy_stock(context, symbol, bar.close)
            elif ma5 < ma20 and has_position(context, symbol):
                sell_stock(context, symbol, bar.close)
```

### 3. RSI策略
```python
def on_bar(context, bars):
    for bar in bars:
        symbol = bar.symbol

        # 获取历史价格计算RSI
        hist_data = context.data(symbol=symbol, frequency='60s', count=30)
        if len(hist_data) >= 15:
            prices = [d['close'] for d in hist_data]
            rsi = calculate_rsi(prices, 14)

            # RSI超卖买入，超买卖出
            if rsi < 30 and not has_position(context, symbol):
                buy_stock(context, symbol, bar.close)
            elif rsi > 70 and has_position(context, symbol):
                sell_stock(context, symbol, bar.close)
```

### 4. 网格交易策略
```python
def init(context):
    context.grid_levels = {}  # 网格价位
    context.grid_size = 0.02  # 网格间距2%

def on_bar(context, bars):
    for bar in bars:
        symbol = bar.symbol
        price = bar.close

        if symbol not in context.grid_levels:
            # 初始化网格
            context.grid_levels[symbol] = create_grid(price, context.grid_size)

        # 网格交易逻辑
        execute_grid_trading(context, symbol, price)
```

---

## 调试技巧

### 1. 日志输出
```python
# 使用context.now获取策略时间
print(f"[{context.now}] 买入: {symbol} {volume}股 @ {price:.2f}元")

# 详细的交易日志
def log_trade(context, action, symbol, volume, price, reason=""):
    timestamp = context.now.strftime('%H:%M:%S')
    print(f"[{timestamp}] {action}: {symbol} {volume}股 @ {price:.2f}元 {reason}")
```

### 2. 数据验证
```python
def validate_price(price, symbol):
    """验证价格有效性"""
    if price <= 0 or price > 10000:
        print(f"警告: {symbol} 价格异常 {price}")
        return False
    return True

def validate_volume(volume):
    """验证数量有效性"""
    return volume > 0 and volume % 100 == 0  # 必须是整手
```

### 3. 性能监控
```python
def on_backtest_finished(context, indicator):
    """回测结束统计"""
    print("="*50)
    print("回测结果:")
    print(f"总收益率: {indicator.pnl_ratio*100:.2f}%")
    print(f"年化收益率: {indicator.pnl_ratio_annual*100:.2f}%")
    print(f"最大回撤: {indicator.max_drawdown*100:.2f}%")
    print(f"夏普比率: {indicator.sharp_ratio:.2f}")
    print(f"交易次数: {len(context.trade_log)}次")
```

---

## 注意事项

1. **数据频率选择**: 根据策略需求选择合适的数据频率
2. **资金管理**: 合理控制单笔交易金额和总持仓比例
3. **错误处理**: 完善的异常处理机制
4. **性能优化**: 指定fields字段，使用row格式
5. **时间处理**: 注意时区问题，使用context.now
6. **品种差异**: 不同品种的交易规则差异
7. **回测vs实盘**: 注意回测和实盘的差异

---

## 快速参考

### 常用导入
```python
from gm.api import *
from datetime import datetime
import numpy as np
import pandas as pd
```

### 常用常量
```python
# 交易方向
OrderSide_Buy, OrderSide_Sell

# 订单类型
OrderType_Market, OrderType_Limit

# 开平仓
PositionEffect_Open, PositionEffect_Close

# 持仓方向
PositionSide_Long, PositionSide_Short

# 复权方式
ADJUST_NONE, ADJUST_PREV, ADJUST_POST

# 运行模式
MODE_BACKTEST, MODE_LIVE
```

这份指南涵盖了掘金SDK的核心功能和最佳实践，可以作为策略开发的完整参考手册——————QXB
