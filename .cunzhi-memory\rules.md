# 开发规范和规则

- 掘金SDK交易规则：普通股票100股起，科创板200股起，当日买入不能当日卖出(T+1)，需要检查available-volume_today计算可卖数量
- Python缩进规则：掘金策略中order_volume()调用和变量赋值必须正确缩进，避免语法错误导致策略无法运行
- 掘金策略调试技巧：当遇到数据问题时，先简化选股条件到最基本的涨幅检查，逐步排查数据获取问题，避免复杂条件掩盖根本问题
- 掘金API数据获取规则：current()返回当前快照，回测模式只有symbol、price、created_at有效，其余字段为0；history()查询历史数据；context.data()查询订阅数据，必须先subscribe()才能使用
- 掘金API回测限制：回测模式下current()函数只返回symbol、price、created_at字段，其余字段如volume、prev_close等都为0，这解释了为什么昨收价格为0的问题
- 掘金API正确用法：get_symbols()可获取指定交易日的完整数据包括pre_close昨收价，get_history_symbol()可获取多日历史数据，这些函数在回测模式下数据完整，不像current()有限制
- 掘金策略数据获取标准：使用history_n()获取历史数据而非current()，使用get_previous_n_trading_dates()获取交易日，数据获取时指定end_time确保时间一致性
- 掘金策略价格获取优先级：1.分钟线数据的close价格 2.history_n()获取日线收盘价 3.昨收价作为备选，多层备选确保价格数据的可靠性
- 掘金策略开发原则：先用固定股票池测试基本功能，确保选股、交易逻辑正常后，再添加动态股票池等复杂功能，避免多个问题同时出现难以调试
- 掘金策略开发标准：使用策略模板.py作为基础框架，只修改handle_minute_check()核心逻辑和技术指标计算，保持交易层、监控层、统计层不变，确保代码质量和复用性
- 掘金API财务数据获取：stk_get_daily_basic()获取股本换手率，stk_get_daily_mktvalue()获取市值数据，stk_get_index_constituents()获取指数成分股，支持多标的批量查询，返回DataFrame格式
- 掘金API多空力量对比：current()函数返回quotes字段包含5档买卖盘口数据，每档包含bid_p(买价)、bid_v(买量)、ask_p(卖价)、ask_v(卖量)，可计算买卖力量对比判断多空强弱
- 掘金API正确用法：get_symbols()不支持exchange参数，应使用stk_get_index_constituents()获取指数成分股来构建全市场股票池，通过多个指数覆盖全市场
- 掘金策略性能优化：大量股票订阅会导致卡顿，应使用沪深300等核心指数（300只股票）而非全市场，分批订阅数据，只订阅必要的频率数据
- 掘金回测模式适配：分钟线订阅在回测中可能不触发，应在日线触发时使用history()函数主动获取当日分钟线数据，模拟日内选股过程
- 掘金DataFrame判断错误修复：context.data()返回DataFrame，不能用if not hist_data判断，应使用hist_data is None or hist_data.empty检查空数据
- 掘金DataFrame数据访问修复：context.data()返回DataFrame，不能用d['close']字典方式访问，应使用hist_data['close'].tolist()或.iloc[-1]方式访问列数据
- Python f-string格式化错误修复：不能在f-string中使用条件表达式如{value:.2f if value else 'N/A'}，应先用变量存储格式化结果再使用
- 掘金回测模式数据获取简化：history()函数在回测中可能不稳定，改用日线bar.open和bar.close计算日内涨幅，避免复杂的分钟线数据获取
- 掘金历史行情数据API：支持60s、300s、900s、1800s、3600s、1d等多种频率，Bar数据包含symbol、frequency、open、close、high、low、amount、volume、bob、eob等字段，数据范围覆盖上市以来
- 掘金策略止盈止损修复：删除重复的handle_minute_check函数，将止盈止损逻辑合并到日线处理中，确保持仓股票能够正常触发卖出条件
- 多空力量对比功能修复：在简化版选股函数中添加current()获取实时盘口数据，计算买卖盘力量比，要求>=1.2倍才通过选股，并正确记录到选股详情中
- 回测模式多空力量对比修复：current()函数在回测中无法获取盘口数据，改用成交量和涨幅模拟多空力量比，基于量价关系估算买卖盘力量对比
- 掘金回测模式限制：current()函数在回测中只返回symbol、price、created_at字段，quotes等盘口数据字段为空，多空力量对比功能只能在实盘模式下使用
- 掘金回测模式获取盘口数据：订阅tick频率数据时可以获取历史quotes盘口数据，通过context.data()访问，但current()函数在回测中无法获取quotes字段
- 掘金tick数据订阅性能优化：批次大小减至30只股票，tick数据只保留1条最新记录，只订阅symbol和quotes字段，添加详细的订阅进度显示
- 掘金策略开发最佳实践：实时模式支持动态参数add_parameter()和log()函数，回测模式下可在init中预先获取全集数据存入字典提高效率，避免反复调用服务器接口
- 掘金免费版tick数据权限限制：只允许下载最近250个交易日的tick数据，查询起始日期不早于2024-07-09，回测历史数据时无法使用tick数据获取盘口信息
- 量比计算修复：量比应该是当日成交量除以近期平均日成交量，修正为使用volumes[-11:-1]计算近10日平均成交量（排除当日），避免包含当日数据导致计算偏差
- 掘金API财务数据获取：stk_get_daily_basic_pt()获取换手率数据，应统一使用此API获取官方换手率，避免使用估算方法导致不准确
- 掘金API市值数据单位：stk_get_daily_mktvalue_pt()返回的a_mv字段单位为万元，30亿市值对应300000万元，显示时需除以10000转换为亿元显示
- 选股时机和批量处理优化：改为14:30批量选股(避免15:15接近收盘)，添加详细筛选日志显示具体过滤原因，批量处理候选股票池而非单个处理，提升选股效率和可观测性
- 选股触发问题修复：放宽时间条件(14:30后任意时间)，添加详细调试信息显示触发条件检查，增加候选股票筛选过程的可观测性，帮助诊断选股失败原因
- 市值数据异常修复：发现API返回异常大的市值数据(千万亿级别)，添加数据检查和修正逻辑，放宽市值上限到1000亿，增加详细调试信息显示实际市值数值
- 变量作用域错误修复：trigger_times变量需要在使用前定义，调整变量定义顺序，添加更详细的市值数据调试信息，修复UnboundLocalError异常
- 恢复每日全量筛选逻辑：股票策略需要每天重新筛选所有条件(市值、换手率、技术指标等)，因为市场数据每天都在变化，确保选股的时效性和准确性
- 指标参数修正：市值上限恢复到200亿，止盈止损参数修正为固定止盈7%、浮动止盈3%、固定止损5%，确保与原始策略要求完全一致
- 策略性能优化：修复选股指标记录问题，防止重复选股，调整止盈止损参数(止盈5%、浮动止盈2%、止损3%)，提升策略胜率和收益表现
- 掘金SDK开发规范：1.必须使用context.mode判断运行模式(1=实时,2=回测)而非时间判断 2.使用context.data()前必须先subscribe()订阅 3.必须实现标准事件处理函数：on_execution_report,on_account_status,on_error 4.Symbol格式必须为"交易所代码.股票代码"如SHSE.600000 5.实时模式仅在交易时段提供数据 6.策略入口使用标准if __name__ == '__main__'格式
- 用户要求合并7个指标的筛选逻辑为一套，删除重复的筛选函数，在一个循环中完成所有指标计算，简化代码长度
- 掘金SDK数据获取修复：context.data()需要先subscribe()订阅才能使用，当获取未订阅股票数据时会报错"获取窗口数据前需先订阅行情"，应改用history_n()函数直接获取历史数据，无需订阅，性能更好
- 掘金SDK history_n()获取当天数据：必须添加end_time=context.now参数才能获取包含当天在内的最新数据，否则可能获取不到当天的数据，这是官方海龟交易法策略的标准用法
- 掘金策略混合数据模式：选股阶段使用history_n()扫描大量股票，买入股票后立即订阅该股票，止盈止损监控使用context.data()利用订阅数据提高效率，订阅count需要设置为MA_SHORT+1支持技术指标计算
- 掘金SDK获取当日开盘价修复：history_n()获取当日开盘价时必须添加end_time=context.now参数，否则可能获取到昨日数据，导致日内涨幅计算错误
- 掘金SDK获取当日开盘价正确方法：实时模式(context.mode==1)必须使用current()函数获取当日开盘价，回测模式(context.mode==2)可以使用history_n()，这是官方Dual Thrust策略的标准做法
- 掘金SDK财务数据修复：市值字段a_mv单位是元需要除以10000转换为万元，换手率数据通常是T-1日的需要使用前一交易日获取，这样可以确保数据的准确性和时效性
- 掘金SDK下单函数修复：将市价单改为限价单避免沪市保护限价问题，添加详细的下单调试信息和错误处理，检查下单结果的有效性，确保order_volume()函数正确调用
- 掘金策略简化完成：删除复杂的信号系统，改为选股后直接买入模式，修复了交易统计为0的问题，删除了execute_trade_signal等信号执行函数，简化了on_bar和on_tick事件处理，修复了缩进错误
- 修复KeyError错误：在on_backtest_finished函数中，将stock_info['date']改为兼容新旧字段名的写法stock_info.get('selection_date', stock_info.get('date', 'N/A'))，解决字段名不匹配导致的运行时错误
- 修改涨幅计算基准：将涨幅计算从对比当日开盘价改为对比昨日收盘价，更新了筛选逻辑、显示信息、字段名（price_change），并保持向后兼容（intraday_change），使涨跌幅计算更符合市场标准
- 掘金换手率数据获取优化：修改为优先获取当天换手率数据，如果当天数据不可用则自动回退到前一交易日数据，确保数据时间一致性和完整性，避免因数据滞后导致的选股偏差
- 掘金换手率数据获取方法优化：改用get_symbols()函数获取turn_rate字段替代stk_get_daily_basic_pt()，确保换手率数据与价格数据时间完全一致，get_symbols返回当日换手率(%)(当日盘后更新)，避免T-1日数据延迟问题
- 掘金换手率数据获取修正：回测模式下应该直接获取当天的换手率数据，去掉复杂的备选日期逻辑，简化为直接使用stk_get_daily_basic_pt获取trade_date当天的turnrate数据，确保与涨幅计算的时间一致性
- 掘金API更新：stk_get_daily_basic_pt函数应替换为stk_get_daily_basic函数获取换手率数据，新函数使用symbol参数而非symbols，使用start_date和end_date参数而非trade_date，确保获取当天的turnrate数据
- 掘金SDK数据获取时间机制：实盘模式下换手率等财务数据有T+1延迟，当天数据通常在20:00-22:00后才可用，需要使用"日期+1"的方法获取当天数据，或者设置回退机制：明天日期→今天日期→昨天日期，确保数据可用性
- 掘金策略最佳实践优化：1.财务数据按日缓存避免重复获取 2.优先使用订阅数据context.data()减少history_n()调用 3.简化订阅管理逻辑统一使用适中数据量 4.遵循定时任务驱动模式 5.减少API调用频率提升性能
- 均线计算使用不复权数据：所有history_n调用都使用adjust=ADJUST_NONE，确保与主流软件如大智慧等的均线计算方式一致，使用原始价格而非复权价格计算移动平均线
- 掘金换手率数据获取最佳方案：使用get_symbols函数获取turn_rate字段，该字段为当日换手率(%)(当日盘后更新)，比stk_get_daily_basic_pt更准确及时，确保获取当天换手率数据与涨幅计算时间一致
- 掘金换手率数据获取关键技巧：使用stk_get_daily_basic_pt时需要将trade_date参数设置为目标日期+1天，即查询明天的日期才能获取到今天的换手率数据。例如要获取7月23日的换手率，需要查询7月24日的数据。这是掘金API的特殊机制，确保换手率与涨幅计算时间一致
- 掘金换手率数据获取回退机制：优先使用日期+1获取当天数据，如果明日数据为空（如周五、节假日前最后交易日），则自动回退使用当日数据。确保在任何情况下都能获取到换手率数据，避免选股失败
- 掘金SDK交易函数官方文档：https://www.myquant.cn/docs2/sdk/python/API介绍/交易函数.html#order-volume-按指定量委托，包含order_volume按指定量委托的详细参数说明和使用方法
- 量比计算修正：改为使用近5日平均成交量计算量比，公式为当日成交量÷近5日平均成交量，数据要求从11天改为6天，更符合市场常用标准
- 掘金SDK文件路径修复：run()函数的filename参数应使用'main.py'而不是__file__，避免路径解析错误导致的TypeError
- 掘金SDK回测时间格式要求：必须使用完整的日期时间格式'yyyy-mm-dd hh:mm:ss'，如'2024-07-01 09:30:00'，不能只使用日期格式'2024-07-01'
- 掘金SDK函数支持问题：set_benchmark函数可能不被支持，应删除或用try-except包装；set_order_cost函数需要用try-except包装以防止错误
- 掘金SDK API函数修正：1.schedule回调函数只需context参数，不需要bar参数 2.使用get_constituents获取指数成分股，不是stk_get_index_constituents 3.使用get_instruments获取股票信息，不是get_symbols 4.不需要set_order_cost设置手续费
- 掘金SDK API最终修正：1.使用stk_get_index_constituents获取指数成分股，get_constituents已弃用 2.get_instruments需要sec_types参数 3.简化风险控制避免复杂API调用 4.函数参数传递trade_date而非依赖context
- 掘金SDK交易函数修正：order_target_percent函数需要position_side和order_type参数，股票交易使用PositionSide_Long和OrderType_Market
- 掘金SDK order_target_percent函数参数顺序：symbol, percent, position_side, order_type, price(可选), order_duration(可选), order_qualifier(可选), account(可选)
- 掘金SDK账户对象结构：account.cash.nav获取总资产，account.cash.available获取可用现金，positions通过account.positions()获取，没有直接的account.nav属性
- 掘金SDK环境限制：multiprocessing并行计算在掘金环境中可能不稳定，返回0个有效因子，建议优先使用串行计算确保稳定性，保留NumPy优化的计算函数提升单线程性能
- 掘金SDK对象属性安全访问：1.execrpt对象使用getattr安全访问filled_volume等属性 2.account.cash对象使用getattr访问nav和available属性 3.positions对象使用get方法访问字典属性 4.所有属性访问都要用try-except包装
- 掘金SDK回测指标属性修正：indicator对象使用open_count而非order_count获取交易次数，所有指标属性访问都要用getattr安全访问，避免AttributeError异常
- 掘金SDK DataFrame判断修复：stk_get_index_constituents返回DataFrame时不能用if constituents判断，会报"The truth value of a DataFrame is ambiguous"错误，应使用if constituents is not None and len(constituents) > 0进行判断
- 掘金SDK中证1000成分股获取：stk_get_index_constituents(index='SHSE.000852')返回DataFrame格式，包含symbol列，使用constituents['symbol'].tolist()提取股票代码列表，共1000只成分股
