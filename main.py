# Shape Skew M 因子策略 - 基于分钟收益率偏度选股 (NumPy优化版)
from gm.api import *
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from multiprocessing import Pool, cpu_count
import warnings
import pickle
import os
warnings.filterwarnings('ignore')

# ==================== 配置参数 ====================
STRATEGY_ID = '58d5321f-5d4b-11f0-b220-00e2699251ab'
TOKEN = '787cec949649eb140ea4ccccb14523af76110c8f'

# 策略参数
LOOKBACK_DAYS = 20        # 计算偏度的历史天数
TOP_N_STOCKS = 30         # 选择偏度最小的股票数量（增加到30只）
REBALANCE_FREQUENCY = 10  # 调仓频率（天）- 全市场计算较慢，降低频率
INITIAL_CASH = 1000000    # 初始资金
MAX_STOCKS_TO_CALC = 500  # 最大计算股票数量（控制计算时间）

# 回测时间 - 必须使用完整的日期时间格式
BACKTEST_START_DATE = '2024-07-01 09:30:00'
BACKTEST_END_DATE = '2024-12-31 15:00:00'

# ==================== 全局变量 ====================
selected_stocks = []      # 当前选中的股票
last_rebalance_date = None  # 上次调仓日期
factor_cache = {}         # 因子缓存
CACHE_FILE = 'shape_skew_m_factor_cache.pkl'  # 缓存文件名

# ==================== 缓存管理函数 ====================
def load_factor_cache():
    """加载因子缓存"""
    global factor_cache
    try:
        if os.path.exists(CACHE_FILE):
            with open(CACHE_FILE, 'rb') as f:
                factor_cache = pickle.load(f)
            print(f"加载缓存成功: {len(factor_cache)}个交易日的因子数据")
            # 显示缓存的日期范围
            if factor_cache:
                dates = sorted(factor_cache.keys())
                print(f"缓存日期范围: {dates[0]} 到 {dates[-1]}")
        else:
            factor_cache = {}
            print("缓存文件不存在，使用空缓存")
    except Exception as e:
        print(f"加载缓存失败: {e}")
        factor_cache = {}

def save_factor_cache():
    """保存因子缓存"""
    try:
        with open(CACHE_FILE, 'wb') as f:
            pickle.dump(factor_cache, f)
        print(f"保存缓存成功: {len(factor_cache)}个交易日的因子数据")
    except Exception as e:
        print(f"保存缓存失败: {e}")

def check_cache_for_date(trade_date):
    """检查指定日期的缓存是否存在"""
    return trade_date in factor_cache

def get_cached_factors(trade_date):
    """获取缓存的因子数据"""
    return factor_cache.get(trade_date, {})

# ==================== NumPy优化函数 ====================
def fast_skew(data):
    """NumPy优化的偏度计算"""
    data = np.asarray(data, dtype=np.float32)
    n = len(data)
    if n < 3:
        return np.nan

    mean = np.mean(data)
    std = np.std(data)
    if std == 0:
        return 0.0

    # 计算偏度
    skew = np.mean(((data - mean) / std) ** 3)
    return float(skew)

def batch_calculate_returns(minute_data):
    """批量计算收益率"""
    if minute_data is None or minute_data.empty:
        return None

    # 使用NumPy向量化计算
    close_prices = minute_data['close'].values.astype(np.float32)
    returns = np.diff(close_prices) / close_prices[:-1]

    # 过滤异常收益率
    valid_mask = (returns >= -0.2) & (returns <= 0.2) & np.isfinite(returns)
    return returns[valid_mask]

def parallel_factor_calculation(symbol_batch):
    """并行计算一批股票的因子"""
    results = {}
    batch_size = len(symbol_batch)
    valid_count = 0

    for i, (symbol, end_date, lookback_days) in enumerate(symbol_batch):
        try:
            factor_value = calculate_shape_skew_m_optimized(symbol, end_date, lookback_days)
            if factor_value is not None:
                results[symbol] = factor_value
                valid_count += 1
        except Exception as e:
            # 在并行环境中减少打印，避免输出混乱
            pass

        # 进度提示（每批次内部）
        if (i + 1) % 25 == 0:
            print(f"  批次进度: {i+1}/{batch_size}, 有效: {valid_count}")

    print(f"批次完成: {valid_count}/{batch_size} 只股票有效")
    return results

# ==================== 核心因子计算函数 ====================
def calculate_shape_skew_m_optimized(symbol, end_date, lookback_days=20):
    """
    NumPy优化版 shape_skew_m 因子计算

    Args:
        symbol: 股票代码
        end_date: 结束日期
        lookback_days: 回看天数

    Returns:
        float: shape_skew_m 因子值（偏度均值）
    """
    try:
        # 获取分钟K线数据
        minute_data = history_n(
            symbol=symbol,
            frequency='60s',
            count=lookback_days * 250,
            fields='close,bob',
            end_time=end_date + ' 15:00:00',
            adjust=ADJUST_NONE,
            df=True
        )

        if minute_data is None or minute_data.empty:
            return None

        # 数据质量检查
        if len(minute_data) < lookback_days * 50:
            return None

        # 使用优化的收益率计算
        returns = batch_calculate_returns(minute_data)
        if returns is None or len(returns) < 200:
            return None

        # 按交易日分组 - 使用NumPy优化
        minute_data['trade_date'] = pd.to_datetime(minute_data['bob']).dt.date
        minute_data = minute_data.iloc[1:]  # 去掉第一行（因为计算收益率会少一行）
        minute_data['returns'] = returns

        # 计算每日偏度
        daily_skewness = []
        for date, group in minute_data.groupby('trade_date'):
            if len(group) >= 50:
                daily_returns = group['returns'].values
                if len(daily_returns) >= 50:
                    # 使用优化的偏度计算
                    skew_value = fast_skew(daily_returns)
                    if not np.isnan(skew_value) and abs(skew_value) < 10:
                        daily_skewness.append(skew_value)

        # 计算偏度均值
        if len(daily_skewness) >= max(5, lookback_days // 4):
            return np.mean(daily_skewness, dtype=np.float32)
        else:
            return None

    except Exception as e:
        return None

# 保留原函数作为备用
def calculate_shape_skew_m(symbol, end_date, lookback_days=20):
    """原版因子计算函数（备用）"""
    return calculate_shape_skew_m_optimized(symbol, end_date, lookback_days)

def validate_factor_data(factors):
    """验证因子数据质量"""
    if not factors:
        return False

    factor_values = list(factors.values())

    # 检查数据量
    if len(factor_values) < 10:
        print(f"警告: 有效因子数据过少 ({len(factor_values)})")
        return False

    # 检查数据分布
    mean_factor = np.mean(factor_values)
    std_factor = np.std(factor_values)

    print(f"因子统计: 均值={mean_factor:.4f}, 标准差={std_factor:.4f}")
    print(f"因子范围: [{np.min(factor_values):.4f}, {np.max(factor_values):.4f}]")

    # 检查是否有异常值
    outliers = [v for v in factor_values if abs(v - mean_factor) > 3 * std_factor]
    if outliers:
        print(f"发现 {len(outliers)} 个异常因子值")

    return True

def get_stock_universe(trade_date):
    """获取股票池 - 中证1000成分股"""
    try:
        # 获取中证1000成分股
        constituents = stk_get_index_constituents(
            index='SHSE.000852',  # 中证1000指数代码
            trade_date=trade_date
        )

        # 处理DataFrame格式
        if constituents is not None and len(constituents) > 0:
            symbols = constituents['symbol'].tolist()
            print(f"获取中证1000成分股: {len(symbols)}只")
            return symbols
        else:
            print("获取中证1000成分股失败: 返回数据为空")
            return []

    except Exception as e:
        print(f"获取中证1000成分股失败: {e}")
        print(f"错误详情: {type(e).__name__}: {str(e)}")
        return []

def calculate_factors_for_universe_parallel(trade_date):
    """并行计算股票池因子 - 高性能版本"""
    global factor_cache

    print(f"\n=== {trade_date} 开始并行计算因子 ===")

    # 获取股票池
    universe = get_stock_universe(trade_date)
    if not universe:
        print("股票池为空，跳过因子计算")
        return {}

    # 限制计算数量以控制时间
    if len(universe) > MAX_STOCKS_TO_CALC:
        print(f"股票池过大({len(universe)}只)，随机选择{MAX_STOCKS_TO_CALC}只进行计算")
        import random
        universe = random.sample(universe, MAX_STOCKS_TO_CALC)

    # 先测试单个股票计算是否正常
    print("测试单个股票计算...")
    test_symbol = universe[0]
    test_result = calculate_shape_skew_m_optimized(test_symbol, trade_date, LOOKBACK_DAYS)
    print(f"测试结果: {test_symbol} = {test_result}")

    if test_result is None:
        print("单个股票计算失败，可能是数据问题，回退到串行计算")
        return calculate_factors_for_universe_serial(trade_date, universe)

    # 准备并行计算的参数
    symbol_batches = []
    batch_size = max(1, len(universe) // 4)  # 分成4个批次

    for i in range(0, len(universe), batch_size):
        batch = [(symbol, trade_date, LOOKBACK_DAYS) for symbol in universe[i:i+batch_size]]
        symbol_batches.append(batch)

    print(f"分成{len(symbol_batches)}个批次并行计算，每批约{batch_size}只股票")

    # 并行计算
    factors = {}
    try:
        print("开始并行计算...")
        with Pool(processes=min(2, len(symbol_batches))) as pool:  # 减少进程数
            results = pool.map(parallel_factor_calculation, symbol_batches)

        print(f"并行计算返回{len(results)}个结果")

        # 合并结果
        for i, result in enumerate(results):
            print(f"批次{i+1}结果: {len(result)}个有效因子")
            factors.update(result)

    except Exception as e:
        print(f"并行计算失败，回退到串行计算: {e}")
        return calculate_factors_for_universe_serial(trade_date, universe)

    print(f"并行计算完成: {len(factors)}/{len(universe)} 只股票有效")

    # 如果并行计算结果太少，回退到串行
    if len(factors) < 10:
        print("并行计算结果过少，回退到串行计算")
        return calculate_factors_for_universe_serial(trade_date, universe)

    # 验证因子数据质量
    if not validate_factor_data(factors):
        print("因子数据质量检查失败，回退到串行计算")
        return calculate_factors_for_universe_serial(trade_date, universe)

    # 缓存结果
    factor_cache[trade_date] = factors
    return factors

def calculate_factors_for_universe_serial(trade_date, universe=None):
    """串行计算因子 - 备用方案"""
    global factor_cache

    if universe is None:
        universe = get_stock_universe(trade_date)
        if not universe:
            return {}

    factors = {}
    valid_count = 0

    for i, symbol in enumerate(universe):
        if i % 30 == 0:
            print(f"串行计算进度: {i+1}/{len(universe)} (有效: {valid_count})")

        factor_value = calculate_shape_skew_m_optimized(symbol, trade_date, LOOKBACK_DAYS)

        if factor_value is not None:
            factors[symbol] = factor_value
            valid_count += 1

        # 提前结束条件
        if valid_count >= TOP_N_STOCKS * 34:
            print(f"已获得足够有效因子({valid_count}个)，提前结束计算")
            break

    factor_cache[trade_date] = factors
    return factors

# 主函数选择计算方式
def calculate_factors_for_universe(trade_date):
    """智能选择计算方式 - 支持缓存"""
    # 检查缓存
    if check_cache_for_date(trade_date):
        cached_factors = get_cached_factors(trade_date)
        print(f"\n=== {trade_date} 使用缓存因子 ===")
        print(f"缓存中有 {len(cached_factors)} 只股票的因子数据")
        return cached_factors

    # 缓存中没有，需要计算
    print(f"\n=== {trade_date} 开始串行计算因子 ===")
    factors = calculate_factors_for_universe_serial(trade_date)

    # 计算完成后保存缓存
    if factors:
        save_factor_cache()
        print(f"因子计算完成并已缓存")

    return factors

def select_stocks_by_factor(factors, top_n=20):
    """根据因子值选股 - 选择偏度最小的股票"""
    if not factors:
        return []

    # 按因子值排序（偏度从小到大）
    sorted_stocks = sorted(factors.items(), key=lambda x: x[1])

    # 选择偏度最小的top_n只股票
    selected = [stock[0] for stock in sorted_stocks[:top_n]]

    print(f"\n=== 选股结果 ===")
    print(f"候选股票总数: {len(factors)}")
    print(f"选中股票数量: {len(selected)}")
    print(f"因子分布: 最小={sorted_stocks[0][1]:.4f}, 最大={sorted_stocks[-1][1]:.4f}")

    # 显示选中的股票详情
    print(f"\n选中股票详情 (按偏度从小到大):")
    print(f"{'排名':<4} {'股票代码':<12} {'偏度值':<10} {'相对排名':<8}")
    print("-" * 40)

    for i, (symbol, factor_value) in enumerate(sorted_stocks[:top_n]):
        relative_rank = (i + 1) / len(factors)
        print(f"{i+1:<4} {symbol:<12} {factor_value:<10.4f} {relative_rank:<8.2%}")

    # 显示未选中的股票统计
    if len(sorted_stocks) > top_n:
        unselected_factors = [x[1] for x in sorted_stocks[top_n:]]
        print(f"\n未选中股票统计:")
        print(f"  数量: {len(unselected_factors)}")
        print(f"  偏度范围: [{min(unselected_factors):.4f}, {max(unselected_factors):.4f}]")
        print(f"  平均偏度: {np.mean(unselected_factors):.4f}")

    return selected

def apply_risk_controls(selected_stocks, trade_date):
    """应用风险控制规则"""
    if not selected_stocks:
        return selected_stocks

    print(f"\n=== 风险控制检查 ===")

    # 简化版风险控制 - 暂时跳过复杂的API调用
    # 基本的股票代码格式检查
    valid_stocks = []

    for symbol in selected_stocks:
        # 基本格式检查
        if '.' in symbol and len(symbol) >= 10:
            # 排除明显的ST股票（基于代码命名）
            if 'ST' not in symbol:
                valid_stocks.append(symbol)
            else:
                print(f"排除ST股票: {symbol}")
        else:
            print(f"股票代码格式异常，排除: {symbol}")

    print(f"风险控制后股票数量: {len(valid_stocks)}/{len(selected_stocks)}")
    return valid_stocks

# ==================== 策略核心函数 ====================
def init(context):
    """策略初始化"""
    global selected_stocks, last_rebalance_date

    print("=== Shape Skew M 因子策略初始化 ===")
    print(f"策略参数:")
    print(f"  股票池: 中证1000成分股")
    print(f"  回看天数: {LOOKBACK_DAYS}")
    print(f"  选股数量: {TOP_N_STOCKS}")
    print(f"  调仓频率: {REBALANCE_FREQUENCY}天")
    print(f"  初始资金: {INITIAL_CASH:,}")

    # 加载因子缓存
    print("\n=== 加载因子缓存 ===")
    load_factor_cache()

    # 掘金SDK不需要手续费设置，使用默认配置
    print("使用默认手续费配置")

    # 初始化变量
    selected_stocks = []
    last_rebalance_date = None

    # 设置定时任务 - 每天收盘后执行
    schedule(schedule_func=daily_check, date_rule='1d', time_rule='15:00:00')

def daily_check(context):
    """每日检查是否需要调仓"""
    global selected_stocks, last_rebalance_date
    
    current_date = context.now.strftime('%Y-%m-%d')
    
    # 检查是否需要调仓
    need_rebalance = False
    
    if last_rebalance_date is None:
        need_rebalance = True
        print(f"{current_date}: 首次调仓")
    else:
        days_since_rebalance = (context.now.date() - datetime.strptime(last_rebalance_date, '%Y-%m-%d').date()).days
        if days_since_rebalance >= REBALANCE_FREQUENCY:
            need_rebalance = True
            print(f"{current_date}: 距离上次调仓{days_since_rebalance}天，执行调仓")
    
    if need_rebalance:
        rebalance_portfolio(context, current_date)

def rebalance_portfolio(context, trade_date):
    """执行调仓"""
    global selected_stocks, last_rebalance_date

    print(f"\n{'='*50}")
    print(f"开始调仓: {trade_date}")
    print(f"{'='*50}")

    # 计算因子并选股
    factors = calculate_factors_for_universe(trade_date)
    if not factors:
        print("因子计算失败，跳过调仓")
        return

    # 根据因子选股
    candidate_stocks = select_stocks_by_factor(factors, TOP_N_STOCKS * 2)  # 选择更多候选股票
    if not candidate_stocks:
        print("选股失败，跳过调仓")
        return

    # 应用风险控制
    new_selected_stocks = apply_risk_controls(candidate_stocks, trade_date)

    # 确保有足够的股票
    if len(new_selected_stocks) < TOP_N_STOCKS // 2:
        print(f"风险控制后股票数量过少 ({len(new_selected_stocks)})，跳过调仓")
        return

    # 取前TOP_N_STOCKS只股票
    new_selected_stocks = new_selected_stocks[:TOP_N_STOCKS]

    print(f"\n=== 执行交易 ===")

    # 清仓不在新选股列表中的股票
    current_positions = context.account().positions()
    for position in current_positions:
        if position['symbol'] not in new_selected_stocks:
            order_target_percent(
                symbol=position['symbol'],
                percent=0,
                position_side=PositionSide_Long,
                order_type=OrderType_Market
            )
            print(f"清仓: {position['symbol']}")

    # 等权重买入新选中的股票
    target_weight = 0.95 / len(new_selected_stocks)  # 保留5%现金

    for symbol in new_selected_stocks:
        order_target_percent(
            symbol=symbol,
            percent=target_weight,
            position_side=PositionSide_Long,
            order_type=OrderType_Market
        )
        print(f"买入: {symbol} (权重: {target_weight:.2%})")

    # 更新全局变量
    selected_stocks = new_selected_stocks
    last_rebalance_date = trade_date

    print(f"调仓完成: 持有{len(selected_stocks)}只股票，目标权重{target_weight:.2%}")

    # 打印组合摘要
    print_portfolio_summary(context)

# ==================== 统计和监控函数 ====================
def print_portfolio_summary(context):
    """打印组合摘要"""
    try:
        account = context.account()
        positions = account.positions()
        cash = account.cash

        print(f"\n=== 组合摘要 ({context.now.strftime('%Y-%m-%d')}) ===")

        # 安全访问现金属性
        try:
            nav = getattr(cash, 'nav', 0)
            available = getattr(cash, 'available', 0)
            print(f"现金净值: {nav:.2f}")
            print(f"可用现金: {available:.2f}")
        except Exception as e:
            print(f"现金信息获取失败: {e}")
            print(f"现金对象: {cash}")

        print(f"持仓数量: {len(positions)}")

        if positions:
            print(f"\n当前持仓:")
            total_market_value = 0
            for pos in positions:
                try:
                    symbol = pos.get('symbol', 'Unknown')
                    volume = pos.get('volume', 0)
                    market_value = pos.get('market_value', 0)
                    total_market_value += market_value
                    print(f"  {symbol}: {market_value:.2f} ({volume}股)")
                except Exception as e:
                    print(f"  持仓信息获取失败: {e}")

            try:
                total_nav = nav + total_market_value
                print(f"\n总资产: {total_nav:.2f}")
                print(f"持仓市值: {total_market_value:.2f}")
            except:
                print(f"\n持仓市值: {total_market_value:.2f}")
        else:
            try:
                print(f"总资产: {nav:.2f}")
            except:
                print(f"总资产: 未知")
            print(f"持仓市值: 0.00")

    except Exception as e:
        print(f"组合摘要获取失败: {e}")
        print(f"账户对象: {context.account()}")

def calculate_portfolio_metrics():
    """计算组合指标"""
    global factor_cache, selected_stocks

    if not selected_stocks or not factor_cache:
        return

    latest_date = max(factor_cache.keys())
    latest_factors = factor_cache[latest_date]

    # 计算持仓股票的因子统计
    holding_factors = [latest_factors.get(symbol, 0) for symbol in selected_stocks if symbol in latest_factors]

    if holding_factors:
        print(f"\n=== 持仓因子分析 ===")
        print(f"平均偏度: {np.mean(holding_factors):.4f}")
        print(f"偏度标准差: {np.std(holding_factors):.4f}")
        print(f"最小偏度: {np.min(holding_factors):.4f}")
        print(f"最大偏度: {np.max(holding_factors):.4f}")

def on_backtest_finished(context, indicator):
    """回测结束处理"""
    print(f"\n{'='*60}")
    print(f"回测完成")
    print(f"{'='*60}")

    # 打印回测指标 - 安全访问属性
    try:
        print(f"总收益率: {getattr(indicator, 'pnl_ratio', 0):.2%}")
        print(f"年化收益率: {getattr(indicator, 'pnl_ratio_annual', 0):.2%}")
        print(f"最大回撤: {getattr(indicator, 'max_drawdown', 0):.2%}")
        print(f"夏普比率: {getattr(indicator, 'sharp_ratio', 0):.4f}")
        print(f"交易次数: {getattr(indicator, 'open_count', getattr(indicator, 'order_count', 0))}")
    except Exception as e:
        print(f"回测指标获取失败: {e}")
        print(f"可用属性: {dir(indicator)}")

    # 打印因子统计
    calculate_portfolio_metrics()

    # 打印最终持仓
    print_portfolio_summary(context)

# ==================== 事件处理函数 ====================
def on_execution_report(context, execrpt):
    """委托执行回报"""
    try:
        # 安全访问执行回报属性
        symbol = getattr(execrpt, 'symbol', 'Unknown')
        side = getattr(execrpt, 'side', 'Unknown')
        filled_volume = getattr(execrpt, 'filled_volume', getattr(execrpt, 'volume', 0))
        filled_price = getattr(execrpt, 'filled_vwap', getattr(execrpt, 'price', 0))

        print(f"委托执行: {symbol} {side} {filled_volume}@{filled_price:.2f}")
    except Exception as e:
        print(f"执行回报处理错误: {e}")
        print(f"执行回报对象: {execrpt}")

def on_account_status(context, account):
    """账户状态更新"""
    pass

def on_error(context, code, info):
    """错误处理"""
    print(f"策略错误 [{code}]: {info}")

# ==================== 策略启动 ====================
if __name__ == '__main__':
    run(strategy_id=STRATEGY_ID,
        filename='main.py',  
        token=TOKEN,
        mode=MODE_BACKTEST,
        backtest_start_time=BACKTEST_START_DATE,
        backtest_end_time=BACKTEST_END_DATE,
        backtest_initial_cash=INITIAL_CASH)
