# 开盘价买入止盈止损策略 - 简化版
from gm.api import *
from datetime import datetime

# ==================== 配置参数 ====================
STRATEGY_ID = '58d5321f-5d4b-11f0-b220-00e2699251ab'
TOKEN = '787cec949649eb140ea4ccccb14523af76110c8f'

# 策略参数
STOP_LOSS_RATIO = 0.01    # 止损比例 1%
TAKE_PROFIT_RATIO = 0.05  # 止盈比例 5%
INITIAL_CASH = 1000000    # 初始资金
POSITION_SIZE_RATIO = 0.1 # 持仓量比例 10%

# 回测时间
BACKTEST_START_DATE = '2025-07-01'
BACKTEST_END_DATE = '2025-07-10'

# 股票池
SYMBOLS = [
    'SZSE.000001',  # 平安银行
    'SZSE.000002',  # 万科A
    'SHSE.600000',  # 浦发银行
    'SHSE.600036',  # 招商银行
    'SHSE.600519'   # 贵州茅台
]

# ==================== 辅助函数 ====================
def get_turnover_rate(symbol, trade_date):
    """
    使用stk_get_daily_basic_pt获取换手率

    Args:
        symbol: 股票代码，如'SHSE.600519'
        trade_date: 交易日期，如'2025-07-01'

    Returns:
        float: 换手率（小数形式），如0.05表示5%
        None: 获取失败时返回None
    """
    try:
        # 使用stk_get_daily_basic_pt获取换手率数据
        basic_data = stk_get_daily_basic_pt(
            symbols=[symbol],
            fields='turnrate',  # 换手率字段
            trade_date=trade_date,
            df=True
        )

        if not basic_data.empty:
            # turnrate字段返回的是百分比形式，需要除以100转换为小数
            turnover_rate = basic_data.iloc[0]['turnrate'] / 100
            print(f"📊 {symbol} 换手率: {turnover_rate*100:.2f}%")
            return turnover_rate
        else:
            print(f"⚠️ 无法获取 {symbol} 的换手率数据")
            return None

    except Exception as e:
        print(f"❌ 获取换手率异常: {e}")
        return None

def check_turnover_condition(symbol, trade_date, min_rate=0.02, max_rate=0.15):
    """
    检查换手率是否符合条件

    Args:
        symbol: 股票代码
        trade_date: 交易日期
        min_rate: 最小换手率（默认2%）
        max_rate: 最大换手率（默认15%）

    Returns:
        bool: True表示符合条件，False表示不符合
    """
    turnover_rate = get_turnover_rate(symbol, trade_date)

    if turnover_rate is None:
        return False

    if min_rate <= turnover_rate <= max_rate:
        print(f"✅ 换手率条件通过: {turnover_rate*100:.2f}% (要求: {min_rate*100:.1f}%-{max_rate*100:.1f}%)")
        return True
    else:
        print(f"❌ 换手率不符合条件: {turnover_rate*100:.2f}% (要求: {min_rate*100:.1f}%-{max_rate*100:.1f}%)")
        return False

# ==================== 策略函数 ====================
def init(context):
    """策略初始化"""
    print("="*60)
    print("开盘价买入止盈止损策略启动")
    print("="*60)
    print(f"止损比例: {STOP_LOSS_RATIO*100:.1f}%")
    print(f"止盈比例: {TAKE_PROFIT_RATIO*100:.1f}%")
    print(f"持仓比例: {POSITION_SIZE_RATIO*100:.0f}%")
    print(f"监控股票: {len(SYMBOLS)}只")
    print(f"回测期间: {BACKTEST_START_DATE} 至 {BACKTEST_END_DATE}")
    print("="*60)

    # 订阅数据（学习雪球策略的订阅方式）
    subscribe(symbols=SYMBOLS, frequency='1d', count=1, unsubscribe_previous=False)    # 日线获取开盘价
    subscribe(symbols=SYMBOLS, frequency='60s', count=1, unsubscribe_previous=False)   # 分钟线监控价格

    # 初始化变量
    context.positions = {}        # 持仓记录
    context.bought_today = set()  # 今日已买入记录
    context.trade_log = []        # 交易日志记录
    context.error_count = 0       # 错误计数器

def on_bar(context, bars):
    """K线数据处理"""
    for bar in bars:
        if bar.frequency == '1d':
            handle_daily_open(context, bar)
        elif bar.frequency == '60s':
            handle_minute_check(context, bar)

def handle_daily_open(context, bar):
    """处理日线开盘 - 买入逻辑"""
    symbol = bar.symbol
    open_price = bar.open

    # 每天重置买入记录
    if not hasattr(context, 'last_date') or context.last_date != bar.eob.date():
        context.bought_today = set()
        context.last_date = bar.eob.date()
        print(f"\n[{context.now}] 新交易日")

    # 检查买入条件
    if (symbol not in context.bought_today and
        symbol not in context.positions and
        len(context.positions) < 2):  # 最多持有2只股票

        buy_stock(context, symbol, open_price)
        context.bought_today.add(symbol)

def handle_minute_check(context, bar):
    """处理分钟线 - 止盈止损检查"""
    symbol = bar.symbol
    current_price = bar.close

    if symbol in context.positions:
        position = context.positions[symbol]
        buy_price = position['price']
        profit_ratio = (current_price - buy_price) / buy_price

        # 止损
        if profit_ratio <= -STOP_LOSS_RATIO:
            sell_stock(context, symbol, current_price, position, "止损")
        # 止盈
        elif profit_ratio >= TAKE_PROFIT_RATIO:
            sell_stock(context, symbol, current_price, position, "止盈")

def buy_stock(context, symbol, price):
    """买入股票 - 改进版"""
    try:
        # 查询账户资金
        account_cash = context.account().cash
        available_cash = account_cash['available']

        # 计算买入股数
        target_amount = min(available_cash * POSITION_SIZE_RATIO, available_cash)
        volume = int(target_amount / price / 100) * 100

        # 科创板特殊处理：最小200股
        if symbol[:7] == 'SHSE.68':
            volume = min(200, volume) if volume > 0 else 200
        elif volume < 100:
            volume = 100

        # 检查资金是否足够
        required_amount = volume * price
        if required_amount > available_cash:
            return

        # 根据品种选择下单方式
        if symbol[:7] in ['SHSE.11', 'SZSE.12']:  # 可转债限价下单
            order = order_volume(symbol=symbol, volume=volume, side=OrderSide_Buy,
                               order_type=OrderType_Limit, position_effect=PositionEffect_Open, price=price)
        else:  # 其他品种市价下单
            order = order_volume(symbol=symbol, volume=volume, side=OrderSide_Buy,
                               order_type=OrderType_Market, position_effect=PositionEffect_Open, price=0)

        if order:
            actual_amount = volume * price
            context.positions[symbol] = {
                'price': price,
                'volume': volume,
                'amount': actual_amount,
                'buy_time': context.now.strftime('%H:%M:%S')
            }
            print(f"[{context.now}] 买入: {symbol} {volume}股 @ {price:.2f}元")
            # 添加交易日志
            add_trade_log(context, symbol, "买入", price, volume)
        else:
            print(f"[{context.now}] 买入失败: {symbol}")
            context.error_count += 1

    except Exception as e:
        print(f"[{context.now}] 买入异常: {symbol} {str(e)}")
        context.error_count += 1

def sell_stock(context, symbol, price, position, reason):
    """卖出股票 - 改进版"""
    try:
        # 查询实际持仓
        account_position = context.account().position(symbol=symbol, side=PositionSide_Long)
        if not account_position:
            return

        # 计算可卖数量（排除今日买入的股票）
        available_volume = account_position['available'] - account_position['volume_today']
        sell_volume = min(position['volume'], available_volume)

        if sell_volume <= 0:
            return

        buy_price = position['price']

        # 根据品种选择下单方式
        if symbol[:7] in ['SHSE.11', 'SZSE.12']:  # 可转债限价下单
            order = order_volume(symbol=symbol, volume=sell_volume, side=OrderSide_Sell,
                               order_type=OrderType_Limit, position_effect=PositionEffect_Close, price=price)
        else:  # 其他品种市价下单
            order = order_volume(symbol=symbol, volume=sell_volume, side=OrderSide_Sell,
                               order_type=OrderType_Market, position_effect=PositionEffect_Close, price=0)

        if order:
            profit = (price - buy_price) * sell_volume
            profit_ratio = (price - buy_price) / buy_price * 100

            # 如果全部卖出，删除持仓记录
            if sell_volume >= position['volume']:
                del context.positions[symbol]
            else:
                # 部分卖出，更新持仓记录
                context.positions[symbol]['volume'] -= sell_volume
                context.positions[symbol]['amount'] = context.positions[symbol]['volume'] * buy_price

            print(f"[{context.now}] {reason}: {symbol} {sell_volume}股 @ {price:.2f}元 "
                  f"盈亏:{profit:.0f}元 ({profit_ratio:+.2f}%)")

            # 添加交易日志
            add_trade_log(context, symbol, "卖出", price, sell_volume, profit, profit_ratio, reason)
        else:
            print(f"[{context.now}] 卖出失败: {symbol}")
            context.error_count += 1

    except Exception as e:
        print(f"[{context.now}] 卖出异常: {symbol} {str(e)}")
        context.error_count += 1

def on_order_status(context, order):
    """订单状态处理 - 简化版"""
    try:
        if order.status == 8:  # 订单被拒绝
            context.error_count += 1
            print(f"[{context.now}] 订单拒绝: {order.symbol}")

            # 处理资金不足的情况
            if '资金不足' in order.ord_rej_reason_detail and order.side == OrderSide_Buy:
                try:
                    account_cash = context.account().cash
                    available_cash = account_cash['available']
                    current_data = current(symbols=order.symbol)[0]
                    # 降低100股重试
                    new_volume = max(min(order.volume - 100,
                                       int(available_cash / current_data['price'] / 100) * 100), 0)

                    if new_volume >= 100:
                        print(f"[{context.now}] 重试: {order.symbol} {new_volume}股")
                        order_volume(symbol=order.symbol, volume=new_volume, side=OrderSide_Buy,
                                   order_type=OrderType_Market, position_effect=PositionEffect_Open, price=0)
                except Exception:
                    context.error_count += 1

        elif order.status == 3:  # 订单完全成交
            side_text = "买入" if order.position_effect == PositionEffect_Open else "卖出"
            print(f"[{context.now}] 成交: {side_text}{order.symbol} {order.filled_volume}股 @ {order.filled_vwap:.2f}元")

    except Exception:
        context.error_count += 1

def add_trade_log(context, symbol, side, price, volume, profit=0, profit_ratio=0, reason=""):
    """添加交易日志"""
    trade_info = {
        'time': context.now,
        'symbol': symbol,
        'side': side,
        'price': price,
        'volume': volume,
        'amount': price * volume,
        'profit': profit,
        'profit_ratio': profit_ratio,
        'reason': reason
    }
    context.trade_log.append(trade_info)

def on_backtest_finished(context, indicator):
    """回测结束 - 美化版统计报告"""
    print("\n" + "="*70)
    print("                           回测完成")
    print("="*70)

    # 交易统计
    buy_count = len([t for t in context.trade_log if t['side'] == "买入"])
    sell_count = len([t for t in context.trade_log if t['side'] == "卖出"])
    profit_trades = [t for t in context.trade_log if t['profit'] > 0]
    loss_trades = [t for t in context.trade_log if t['profit'] < 0]
    total_profit = sum([t['profit'] for t in context.trade_log])
    avg_profit = total_profit / max(1, sell_count)

    # 回测指标
    print(" 📊 回测指标:")
    print("-" * 70)
    print(f"总收益率: {indicator.pnl_ratio*100:.2f}%")
    print(f"年化收益率: {indicator.pnl_ratio_annual*100:.2f}%")
    print(f"最大回撤: {indicator.max_drawdown*100:.2f}%")
    print(f"夏普比率: {indicator.sharp_ratio:.2f}")
    print(f"交易次数: {buy_count+sell_count}次")
    print(f"胜率: {len(profit_trades)/max(1,len(profit_trades)+len(loss_trades))*100:.1f}%")

    print("="*70)
    print(" 📈 策略统计:")
    print(f"信号生成次数: {getattr(context, 'signal_count', buy_count)}")
    print(f"监控股票数量: {len(SYMBOLS)}")

    # 最终持仓
    if context.positions:
        print("\n 💼 最终持仓:")
        for symbol, pos in context.positions.items():
            print(f"{symbol}: 买入价 {pos['price']:.2f}元")

    print("\n" + "="*70)
    print("                        交易统计报告")
    print("="*70)
    print(f"总交易次数: {buy_count + sell_count}")
    print(f"盈利次数: {len(profit_trades)}")
    print(f"亏损次数: {len(loss_trades)}")
    print(f"胜率: {len(profit_trades)/max(1,len(profit_trades)+len(loss_trades))*100:.2f}%")
    print(f"总盈亏: {total_profit:.2f}元")
    print(f"平均每笔盈亏: {avg_profit:.2f}元")
    print("="*70)

# ==================== 主程序 ====================
if __name__ == '__main__':
    print("="*50)
    print("开盘价买入止盈止损策略")
    print("="*50)
    
    # 设置回测参数
    set_token(TOKEN)
    
    # 运行回测
    run(strategy_id=STRATEGY_ID,
        filename='main.py',
        mode=MODE_BACKTEST,
        token=TOKEN,
        backtest_start_time=f'{BACKTEST_START_DATE} 09:30:00',
        backtest_end_time=f'{BACKTEST_END_DATE} 15:00:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=INITIAL_CASH,
        backtest_commission_ratio=0.0003,
        backtest_slippage_ratio=0.0001)
